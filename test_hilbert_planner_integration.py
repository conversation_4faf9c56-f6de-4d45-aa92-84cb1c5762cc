#!/usr/bin/env python3
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Test script for Hilbert curve integration in the planner.

This script tests that the planner correctly applies Hilbert curve sorting
for spatial locality while preserving temporal diversity across scenes.
"""

import sys
import logging

# Add the libs to the path
sys.path.insert(0, 'libs/tfw_engine_core/src')

from terrafloww.engine_core.spatial_utils import hilbert_index, test_hilbert_spatial_locality

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_hilbert_sorting_logic():
    """Test the Hilbert sorting logic used in the planner."""
    logger.info("Testing Hilbert sorting logic...")
    
    # Create test windows similar to what the planner would generate
    test_windows = [
        {'scene_id': 'scene1', 'tile_r': 0, 'tile_c': 0},
        {'scene_id': 'scene1', 'tile_r': 0, 'tile_c': 1},
        {'scene_id': 'scene1', 'tile_r': 1, 'tile_c': 0},
        {'scene_id': 'scene1', 'tile_r': 1, 'tile_c': 1},
        {'scene_id': 'scene2', 'tile_r': 0, 'tile_c': 0},
        {'scene_id': 'scene2', 'tile_r': 0, 'tile_c': 1},
        {'scene_id': 'scene2', 'tile_r': 1, 'tile_c': 0},
        {'scene_id': 'scene2', 'tile_r': 1, 'tile_c': 1},
    ]
    
    # Group by scene and sort by Hilbert index (mimicking planner logic)
    windows_by_scene = {}
    hilbert_order = 16
    
    for window in test_windows:
        scene_id = window['scene_id']
        if scene_id not in windows_by_scene:
            windows_by_scene[scene_id] = []
        
        # Compute Hilbert index
        hilbert_idx = hilbert_index(window['tile_r'], window['tile_c'], hilbert_order)
        windows_by_scene[scene_id].append((window, hilbert_idx))
    
    # Sort each scene by Hilbert index
    for scene_id in windows_by_scene:
        windows_by_scene[scene_id].sort(key=lambda x: x[1])
        logger.info(f"Scene {scene_id} Hilbert-sorted windows:")
        for window, hilbert_idx in windows_by_scene[scene_id]:
            logger.info(f"  Hilbert {hilbert_idx:3d}: tile ({window['tile_r']}, {window['tile_c']})")
    
    # Verify spatial locality within each scene
    for scene_id, windows in windows_by_scene.items():
        locality_score = 0
        for i in range(len(windows) - 1):
            window1, _ = windows[i]
            window2, _ = windows[i + 1]
            
            # Manhattan distance between adjacent tiles in Hilbert order
            distance = abs(window2['tile_r'] - window1['tile_r']) + abs(window2['tile_c'] - window1['tile_c'])
            if distance <= 2:  # Adjacent or diagonal tiles
                locality_score += 1
        
        locality_ratio = locality_score / (len(windows) - 1) if len(windows) > 1 else 1.0
        logger.info(f"Scene {scene_id} spatial locality: {locality_score}/{len(windows)-1} = {locality_ratio:.2f}")
        
        assert locality_ratio >= 0.5, f"Poor spatial locality in scene {scene_id}: {locality_ratio}"
    
    logger.info("✓ Hilbert sorting logic test passed")

def test_temporal_diversity_preservation():
    """Test that temporal diversity is preserved across scenes."""
    logger.info("Testing temporal diversity preservation...")
    
    # Create test windows from multiple scenes
    test_windows = []
    for scene_idx in range(3):  # 3 scenes
        for tile_r in range(2):  # 2x2 grid per scene
            for tile_c in range(2):
                test_windows.append({
                    'scene_id': f'scene{scene_idx}',
                    'tile_r': tile_r,
                    'tile_c': tile_c
                })
    
    # Group by scene and sort by Hilbert index
    windows_by_scene = {}
    hilbert_order = 16
    
    for window in test_windows:
        scene_id = window['scene_id']
        if scene_id not in windows_by_scene:
            windows_by_scene[scene_id] = []
        
        hilbert_idx = hilbert_index(window['tile_r'], window['tile_c'], hilbert_order)
        windows_by_scene[scene_id].append((window, hilbert_idx))
    
    # Sort each scene by Hilbert index
    for scene_id in windows_by_scene:
        windows_by_scene[scene_id].sort(key=lambda x: x[1])
    
    # Interleave scenes to preserve temporal diversity
    interleaved_windows = []
    scene_ids = list(windows_by_scene.keys())
    max_windows_per_scene = max(len(windows) for windows in windows_by_scene.values())
    
    for i in range(max_windows_per_scene):
        for scene_id in scene_ids:
            if i < len(windows_by_scene[scene_id]):
                window, hilbert_idx = windows_by_scene[scene_id][i]
                interleaved_windows.append(window)
    
    # Verify temporal diversity: scenes should be interleaved
    logger.info("Interleaved window order (preserving temporal diversity):")
    scene_sequence = []
    for i, window in enumerate(interleaved_windows):
        scene_id = window['scene_id']
        scene_sequence.append(scene_id)
        logger.info(f"  {i:2d}: {scene_id} tile ({window['tile_r']}, {window['tile_c']})")
    
    # Check that scenes are reasonably interleaved
    # Count scene transitions
    scene_transitions = 0
    for i in range(len(scene_sequence) - 1):
        if scene_sequence[i] != scene_sequence[i + 1]:
            scene_transitions += 1
    
    # Should have multiple scene transitions for good temporal diversity
    min_expected_transitions = len(scene_ids) - 1  # At least one transition per scene
    assert scene_transitions >= min_expected_transitions, f"Poor temporal diversity: only {scene_transitions} transitions"
    
    logger.info(f"✓ Temporal diversity preserved: {scene_transitions} scene transitions")

def main():
    """Run all Hilbert planner integration tests."""
    logger.info("Starting Hilbert planner integration tests...")
    
    try:
        # Test basic Hilbert curve functionality
        if not test_hilbert_spatial_locality():
            return False
        
        # Test planner-specific logic
        test_hilbert_sorting_logic()
        test_temporal_diversity_preservation()
        
        logger.info("🎉 All Hilbert planner integration tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
