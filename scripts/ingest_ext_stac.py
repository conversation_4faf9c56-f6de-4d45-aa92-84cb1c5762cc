# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# src/terrafloww/scripts/ingest_ext_stac.py
import asyncio
import argparse
import logging
import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple

import httpx
import pystac_client
from pystac import Item
import pyarrow as pa
import pyarrow.compute as pc
from deltalake import write_deltalake
from shapely.geometry import shape as shapely_shape
import pandas as pd  # For timestamp parsing robustly

# S3/Cloud storage imports
try:
    import boto3
    from botocore.exceptions import ClientError
    S3_AVAILABLE = True
except ImportError:
    S3_AVAILABLE = False

# --- Assume schema is defined here or imported ---
from tfw_raster_schemas.stac import EXT_STAC_IMG_DATASETS_SCHEMA, PARTITION_COLS, ASSETS_TO_PROCESS

from terrafloww.engine_core.parser import (
    parse_cog_header_info as _parse_cog_header_info_internal,
)

# --- Logging Setup ---
logging.basicConfig(
    level=logging.DEBUG, format="%(asctime)s - %(levelname)s - [%(name)s] %(message)s"
)
logger = logging.getLogger("ingest_ext_stac")
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("botocore").setLevel(logging.WARNING)  # Silence noisy logs if using S3

# --- Configuration ---
def get_default_output_path() -> str:
    """Get default output path based on environment configuration."""
    # Check for S3 configuration first
    s3_bucket = os.environ.get("STAC_CATALOG_S3_BUCKET")
    s3_endpoint = os.environ.get("STAC_CATALOG_S3_ENDPOINT") or os.environ.get("DO_SPACE_ENDPOINT")
    s3_path_prefix = os.environ.get("STAC_CATALOG_S3_PATH_PREFIX", "")

    if s3_bucket and s3_endpoint:
        # Use S3 path
        if s3_path_prefix:
            return f"s3://{s3_bucket}/{s3_path_prefix}/ext_stac_datasets"
        else:
            return f"s3://{s3_bucket}/ext_stac_datasets"
    else:
        # Fall back to local path
        delta_lake_root = os.environ.get("TFW_DELTA_LAKE_ROOT", "/tmp/platform_delta_tables")
        return str(Path(delta_lake_root) / "ext_stac_datasets")

def get_s3_storage_options() -> Optional[Dict[str, str]]:
    """Get S3 storage options for Delta Lake if S3 is configured."""
    s3_bucket = os.environ.get("STAC_CATALOG_S3_BUCKET")
    s3_endpoint = os.environ.get("STAC_CATALOG_S3_ENDPOINT") or os.environ.get("DO_SPACE_ENDPOINT")

    if not (s3_bucket and s3_endpoint):
        return None

    # Get credentials from environment
    access_key = os.environ.get("DO_ACCESS_KEY_ID") or os.environ.get("AWS_ACCESS_KEY_ID")
    secret_key = os.environ.get("DO_SECRET_ACCESS_KEY") or os.environ.get("AWS_SECRET_ACCESS_KEY")
    region = os.environ.get("STAC_CATALOG_S3_REGION") or os.environ.get("DO_REGION", "nyc3")

    if not (access_key and secret_key):
        logger.warning("S3 bucket configured but credentials not found in environment")
        return None

    storage_options = {
        "AWS_ACCESS_KEY_ID": access_key,
        "AWS_SECRET_ACCESS_KEY": secret_key,
        "AWS_REGION": region,
    }

    if s3_endpoint:
        storage_options["AWS_ENDPOINT_URL"] = s3_endpoint
        storage_options["AWS_S3_ALLOW_UNSAFE_RENAME"] = "true"

    return storage_options

# Get default paths
EXT_STAC_TABLE_PATH = get_default_output_path()

# --- Core Processing Function ---
async def parse_cog_header_wrapper(
    cog_key: str, cog_href: str, http_client: httpx.AsyncClient
) -> Tuple[str, Optional[Dict[str, Any]]]:
    """Wrapper to call the internal parser and handle errors."""
    if not cog_href:
        logger.warning(f"Asset '{cog_key}' has no URL.")
        return cog_key, None
    try:
        logger.debug(f"Parsing header for {cog_key} @ {cog_href}")
        header_info = await _parse_cog_header_info_internal(
            client=http_client, url=cog_href
        )
        if header_info is None:
            logger.error(f"Failed to parse header for asset '{cog_key}' URL: {cog_href}")
            return cog_key, None

        # Ensure all required keys are present before returning
        required_keys = [
            "width",
            "height",
            "tile_width",
            "tile_height",
            "transform",
            "crs_string",
            "dtype_str",
            "predictor",
            "tile_offsets",
            "tile_byte_counts",
        ]
        if not all(k in header_info for k in required_keys):
            logger.error(
                f"Parsed header for asset '{cog_key}' is missing required keys. Got: {header_info.keys()}"
            )
            return cog_key, None

        # Add compression info if available
        header_info["compression_str"] = str(header_info.get("compression", "unknown"))

        return cog_key, header_info
    except httpx.HTTPStatusError as e:
        logger.error(
            f"HTTP error {e.response.status_code} parsing header for asset '{cog_key}' URL: {cog_href} - {e.request.url}"
        )
        return cog_key, None
    except httpx.RequestError as e:
        logger.error(f"Request error parsing header for asset '{cog_key}' URL: {cog_href} - {e}")
        return cog_key, None
    except Exception as e:
        logger.exception(f"Unexpected error parsing header for asset '{cog_key}' URL: {cog_href}: {e}")
        return cog_key, None


async def process_stac_item_to_cog_rows(
    item: Item, collection_id: str, http_client: httpx.AsyncClient
) -> List[Dict[str, Any]]:
    """
    Processes a single STAC item and returns a list of dictionaries,
    each representing a row for the ext_stac_datasets table (one per asset).
    """
    cog_rows = []
    logger.debug(f"Processing item: {item.id}")

    try:
        # --- Extract Scene-Level Info ---
        scene_id = item.id
        dt_str = item.datetime.isoformat()  # Get standard ISO format string
        # Use pandas for robust timestamp parsing and UTC conversion/awareness
        scene_datetime = pd.Timestamp(dt_str).tz_convert("UTC")
        if scene_datetime.tzinfo is None:  # Ensure timezone awareness
            scene_datetime = scene_datetime.tz_localize("UTC")

        scene_geometry = shapely_shape(item.geometry) if item.geometry else None
        scene_geometry_wkt = scene_geometry.wkt if scene_geometry else None
        bbox = item.bbox or (scene_geometry.bounds if scene_geometry else None)
        if bbox and len(bbox) == 4:
            bbox_minx, bbox_miny, bbox_maxx, bbox_maxy = bbox
        else:
            bbox_minx, bbox_miny, bbox_maxx, bbox_maxy = None, None, None, None

        # Extract cloud cover robustly
        cloud_cover = item.properties.get("eo:cloud_cover")
        if cloud_cover is None:  # Check common alternative names
            cloud_cover = item.properties.get("cloud_cover")
        if not isinstance(cloud_cover, (int, float)):
            cloud_cover = None  # Set to None if not a valid number

        year = scene_datetime.year
        month = scene_datetime.month

        # Store original properties/assets as JSON
        stac_properties_json = json.dumps(item.properties)
        # Filter assets to only include those with href for storage
        stac_assets_dict = {k: v.to_dict() for k, v in item.assets.items() if v.href}
        stac_assets_json = json.dumps(stac_assets_dict)

        # --- Identify and Parse Relevant COG Assets ---
        cog_keys_to_parse = ASSETS_TO_PROCESS.get(collection_id, [])
        tasks = []
        cog_metadata_map = {}  # To store original STAC asset metadata

        for key in cog_keys_to_parse:
            if key in item.assets:
                asset = item.assets[key]
                cog_href = asset.get_absolute_href()
                # Basic check for COG potential (can be refined)
                is_cog_likely = (
                    cog_href
                    and (asset.media_type and "image/tiff" in asset.media_type.lower())
                    and (
                        "profile=cloud-optimized" in asset.media_type.lower()
                        if asset.media_type
                        else True
                    )  # Be lenient if profile missing
                )
                if is_cog_likely:
                    logger.debug(f"Adding asset '{key}' for header parsing.")
                    tasks.append(parse_cog_header_wrapper(key, cog_href, http_client))
                    cog_metadata_map[key] = (
                        asset.to_dict()
                    )  # Store original asset info
                else:
                    logger.debug(
                        f"Skipping asset '{key}' (not COG or no href). Media type: {asset.media_type}"
                    )
            else:
                logger.debug(f"Asset key '{key}' not found in item {scene_id}.")

        if not tasks:
            logger.warning(
                f"No suitable COG assets found to parse for item {scene_id}. Skipping item."
            )
            return []

        logger.info(f"Parsing {len(tasks)} asset headers for item {scene_id}...")
        parsed_header_results = await asyncio.gather(*tasks)

        # --- Create Rows for Successfully Parsed Assets ---
        for cog_key, header_info in parsed_header_results:
            if header_info:
                try:
                    # Get the original STAC asset metadata we stored earlier
                    original_cog_info = cog_metadata_map.get(cog_key, {})
                    # --- CORRECTED: Get href from the original asset info ---
                    cog_href = original_cog_info.get("href")
                    if not cog_href:
                        logger.warning(
                            f"Missing href for asset '{cog_key}' in item {scene_id}. Skipping."
                        )
                        continue

                    # Extract scale and offset from raster:bands extension if available
                    cog_scale = None
                    cog_offset = None
                    raster_bands = original_cog_info.get("raster:bands", [])
                    if raster_bands and isinstance(raster_bands, list) and len(raster_bands) > 0:
                        # Take the first band's scale and offset if available
                        first_band = raster_bands[0]
                        if isinstance(first_band, dict):
                            cog_scale = first_band.get("scale")
                            cog_offset = first_band.get("offset")
                            
                    # If not found in raster:bands, check for scale and offset directly in asset properties
                    if cog_scale is None:
                        cog_scale = original_cog_info.get("scale")
                    if cog_offset is None:
                        cog_offset = original_cog_info.get("offset")

                    # Create row for this asset
                    row = {
                        # Scene Info
                        "scene_id": scene_id,
                        "collection": collection_id,
                        "datetime": scene_datetime,
                        "year": year,
                        "month": month,
                        "geometry_wkt": scene_geometry_wkt,
                        "bbox_minx": bbox_minx,
                        "bbox_miny": bbox_miny,
                        "bbox_maxx": bbox_maxx,
                        "bbox_maxy": bbox_maxy,
                        "cloud_cover": cloud_cover,
                        "stac_properties_json": stac_properties_json,
                        "stac_assets_json": stac_assets_json,
                        # Asset Info
                        "cog_key": cog_key,
                        "cog_href": cog_href,  # Use href from header info which should be absolute
                        "cog_title": original_cog_info.get("title"),
                        "cog_roles": original_cog_info.get("roles"),
                        # Parsed COG Info
                        "cog_width": header_info["width"],
                        "cog_height": header_info["height"],
                        "cog_tile_width": header_info["tile_width"],
                        "cog_tile_height": header_info["tile_height"],
                        "cog_transform": header_info[
                            "transform"
                        ],  # Expecting list[float, 6]
                        "cog_crs": header_info["crs_string"],
                        "cog_dtype": header_info["dtype_str"],
                        "cog_predictor": header_info["predictor"],
                        "cog_compression": header_info.get(
                            "compression_str"
                        ),  # Use the string version
                        "cog_tile_offsets": header_info["tile_offsets"],
                        "cog_tile_byte_counts": header_info["tile_byte_counts"],
                        "cog_scale": float(cog_scale) if cog_scale is not None else None,
                        "cog_offset": float(cog_offset) if cog_offset is not None else None,
                    }
                    cog_rows.append(row)
                except Exception as row_err:
                    logger.error(
                        f"Error formatting row for asset '{cog_key}' in item {scene_id}: {row_err}",
                        exc_info=True,
                    )
            else:
                # Header parsing failed for this asset, already logged in wrapper
                logger.warning(
                    f"Skipping asset '{cog_key}' in item {scene_id} due to parsing failure."
                )

    except Exception as item_err:
        logger.error(
            f"Failed to process STAC item {getattr(item, 'id', 'UNKNOWN_ID')}: {item_err}",
            exc_info=True,
        )
        return []  # Return empty list on item-level failure

    if not cog_rows:
        logger.warning(f"No assets successfully processed for item {scene_id}.")

    return cog_rows


# --- Define the Casting Function ---
# --- Revised ensure_table_schema function ---

def ensure_table_schema(
    input_table: pa.Table,
    target_schema: pa.Schema,
    table_description: str = "Input Table"
    ) -> pa.Table:
    """
    Creates a new table by casting columns from the input table to match the
    target schema precisely, including nullability and types.
    """
    logger.debug(f"--- Ensuring Schema for {table_description} ---")
    if input_table.schema.equals(target_schema):
        logger.debug(f"Schema for {table_description} already matches target.")
        return input_table

    logger.info(f"Rebuilding table for {table_description} to match target schema.")
    logger.debug(f"Source Schema (Inferred):\n{input_table.schema}")
    logger.debug(f"Target Schema:\n{target_schema}")

    corrected_arrays = []
    for field in target_schema:
        col_name = field.name
        target_type = field.type
        logger.debug(f"Processing column '{col_name}' -> Target type: {target_type}")
        try:
            # Get the column from the input table created by from_pylist
            current_column = input_table.column(col_name)
            if not current_column.type.equals(target_type):
                 logger.info(f"Casting '{col_name}' from {current_column.type} to {target_type}")
                 # Perform the cast - this should handle compatible types
                 # and fixed/variable list differences if data is valid.
                 # Cast also typically handles nullability based on target type.
                 casted_column = current_column.cast(target_type)
                 corrected_arrays.append(casted_column)
            else:
                 # Type already matches, append as is
                 corrected_arrays.append(current_column)
        except KeyError:
            logger.error(f"Column '{col_name}' missing in source table derived from pylist.")
            raise ValueError(f"Cannot create table: Column '{col_name}' missing.")
        except (pa.ArrowInvalid, pa.ArrowTypeError, TypeError, ValueError) as e:
            logger.error(f"Failed to cast column '{col_name}' to type {target_type}: {e}", exc_info=True)
            # Optionally add more debugging about the specific data causing the cast error
            # logger.debug(f"Data sample for '{col_name}': {current_column.slice(0, 10)}")
            raise ValueError(f"Failed casting column '{col_name}'") from e
        except Exception as e: # Catch any other unexpected errors during processing
             logger.error(f"Unexpected error processing column '{col_name}': {e}", exc_info=True)
             raise e

    # Create the new table directly with the corrected arrays and the target schema
    try:
        final_table = pa.Table.from_arrays(corrected_arrays, schema=target_schema)
        logger.info(f"Successfully rebuilt table for {table_description} matching target schema.")
        logger.debug(f"Final Schema:\n{final_table.schema}")
        return final_table
    except Exception as e:
        logger.error(f"Failed to create final table from corrected arrays: {e}", exc_info=True)
        raise ValueError("Failed to create final table after casting") from e


# --- Main Execution Logic ---


async def main(args, output_path: str):
    """Main ingestion function."""
    logger.info(f"Starting STAC ingest for collection: {args.collection}")
    logger.info(f"STAC API: {args.stac_api}")
    logger.info(f"Output Delta Table: {output_path}")
    logger.info(f"Batch size: {args.batch_size}")

    # Get S3 storage options if configured
    storage_options = get_s3_storage_options()
    if storage_options:
        logger.info("Using S3 storage backend for Delta Lake")
        logger.info(f"S3 endpoint: {storage_options.get('AWS_ENDPOINT_URL', 'default')}")
        logger.info(f"S3 region: {storage_options.get('AWS_REGION', 'default')}")
    else:
        logger.info("Using local filesystem for Delta Lake")
        # Ensure output directory exists (for local filesystem only)
        if not output_path.startswith("s3://"):
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)

    # Initialize STAC Client
    try:
        stac_client = pystac_client.Client.open(args.stac_api)
        logger.info(f"Opened STAC client for {args.stac_api}")
    except Exception as client_err:
        logger.error(f"Failed to open STAC client: {client_err}")
        return

    # Prepare STAC Search Parameters
    search_params: Dict[str, Any] = {
        "collections": [args.collection],
        "limit": args.limit,  # Limit per page request
        "max_items": args.max_items,  # Total limit across pages
    }
    if args.bbox:
        try:
            search_params["bbox"] = [float(c.strip()) for c in args.bbox.split(",")]
            if len(search_params["bbox"]) != 4:
                raise ValueError("Bbox needs 4 values")
        except ValueError as e:
            logger.error(
                f"Invalid bbox format: {args.bbox}. Expected 'min_lon,min_lat,max_lon,max_lat'. Error: {e}"
            )
            return
    if args.datetime:
        search_params["datetime"] = args.datetime

    logger.info(f"Searching STAC with params: {search_params}")

    total_processed_items = 0
    total_cog_rows = 0
    processed_batches = 0

    # Use shared HTTP client for header parsing across batches
    # Configure limits appropriate for potentially many small requests
    limits = httpx.Limits(
        max_connections=args.max_concurrent, max_keepalive_connections=50
    )
    timeout = httpx.Timeout(60.0, connect=10.0)
    async with httpx.AsyncClient(
        limits=limits,
        timeout=timeout,
        http2=True,
        follow_redirects=True,
        trust_env=True,
    ) as http_client:
        try:
            # Use item_search which returns an iterator - more memory efficient
            search = stac_client.search(**search_params)
            item_iterator = search.items()  # Get item iterator

            batch_tasks = []
            batch_item_ids = []
            for item in item_iterator:
                if not isinstance(item, Item):
                    logger.warning(
                        f"Received non-Item object from iterator: {type(item)}"
                    )
                    continue

                batch_tasks.append(
                    process_stac_item_to_cog_rows(item, args.collection, http_client)
                )
                batch_item_ids.append(item.id)

                if len(batch_tasks) >= args.batch_size:
                    batch_num_str = f"Batch {processed_batches + 1}"

                    logger.info(
                        f"Processing batch of {len(batch_tasks)} items (starting with {batch_item_ids[0]})..."
                    )
                    batch_results_list = await asyncio.gather(*batch_tasks)

                    # Flatten results (list of lists of dicts)
                    all_rows_in_batch = [
                        row
                        for sublist in batch_results_list
                        if sublist
                        for row in sublist
                    ]

                    if all_rows_in_batch:
                        logger.info(
                            f"Writing {len(all_rows_in_batch)} asset rows to Delta table at {output_path}..."
                        )  # Use output_path
                        try:
                            batch_table = pa.Table.from_pylist(
                                all_rows_in_batch, schema=EXT_STAC_IMG_DATASETS_SCHEMA
                            )

                            logger.debug(f"{batch_num_str}: Schema inferred by from_pylist:\n{batch_table.schema}")

                            # --- Apply Schema Correction ---
                            batch_table = ensure_table_schema(batch_table, EXT_STAC_IMG_DATASETS_SCHEMA, batch_num_str)
                            # ---

                            logger.info(f"{batch_num_str}: Writing {len(batch_table)} rows to Delta table...")

                            # Prepare write_deltalake arguments
                            write_args = {
                                "table_or_uri": output_path,
                                "data": batch_table,
                                "partition_by": PARTITION_COLS,
                                "mode": "append",
                                "engine": "rust",
                                "schema_mode": "merge",  # Allow adding columns later if needed, but check compatibility
                            }

                            # Add storage options if using S3
                            if storage_options:
                                write_args["storage_options"] = storage_options

                            write_deltalake(**write_args)
                            total_cog_rows += len(all_rows_in_batch)
                            processed_batches += 1
                            logger.info(
                                f"Successfully wrote batch {processed_batches}."
                            )
                        except Exception as write_err:
                            logger.exception(
                                f"Failed to write batch to Delta table at {output_path}: {write_err}"
                            )  # Use output_path
                            # Decide whether to stop or continue
                            # For now, log and continue with next batch
                    else:
                        logger.info("No asset rows generated from this batch.")

                    total_processed_items += len(batch_tasks)
                    batch_tasks = []  # Reset for next batch
                    batch_item_ids = []

            # Process any remaining items in the last batch
            if batch_tasks:
                batch_num_str = f"Final Batch {processed_batches + 1}"

                logger.info(f"Processing final batch of {len(batch_tasks)} items...")
                batch_results_list = await asyncio.gather(*batch_tasks)
                all_rows_in_batch = [
                    row for sublist in batch_results_list if sublist for row in sublist
                ]
                if all_rows_in_batch:
                    logger.info(
                        f"Writing final {len(all_rows_in_batch)} asset rows to Delta table at {output_path}..."
                    )  # Use output_path
                    try:

                        # Create table, allowing potential schema inference issues
                        batch_table = pa.Table.from_pylist(
                            all_rows_in_batch, schema=EXT_STAC_IMG_DATASETS_SCHEMA
                        )
                        logger.debug(f"{batch_num_str}: Schema inferred by from_pylist:\n{batch_table.schema}")

                        # --- Apply Schema Correction ---
                        batch_table = ensure_table_schema(batch_table, EXT_STAC_IMG_DATASETS_SCHEMA, batch_num_str)
                        # ---
                        logger.info(f"{batch_num_str}: Writing {len(batch_table)} rows to Delta table...")


                        # Prepare write_deltalake arguments
                        write_args = {
                            "table_or_uri": output_path,
                            "data": batch_table,
                            "partition_by": PARTITION_COLS,
                            "mode": "append",
                            "engine": "rust",
                            "schema_mode": "merge",
                        }

                        # Add storage options if using S3
                        if storage_options:
                            write_args["storage_options"] = storage_options

                        write_deltalake(**write_args)
                        total_cog_rows += len(all_rows_in_batch)
                        processed_batches += 1
                        logger.info(
                            f"Successfully wrote final batch {processed_batches}."
                        )
                    except Exception as write_err:
                        logger.exception(
                            f"Failed to write final batch to Delta table: {write_err}"
                        )
                else:
                    logger.info("No asset rows generated from final batch.")
                total_processed_items += len(batch_tasks)

        except pystac_client.exceptions.APIError as stac_err:
            logger.error(f"STAC API Error during search/iteration: {stac_err}")
        except Exception as e:
            logger.exception(f"An error occurred during STAC item processing: {e}")

    logger.info(f"Ingestion finished. Processed {total_processed_items} STAC items.")
    logger.info(
        f"Wrote {total_cog_rows} asset rows to {output_path} across {processed_batches} batches."
    )


# --- CLI Entry Point ---
def main_cli():
    """Command Line Interface entry point."""
    parser = argparse.ArgumentParser(
        description="Ingest STAC items into platform external STAC Delta table."
    )
    parser.add_argument("stac_api", help="URL of the STAC API endpoint.")
    parser.add_argument(
        "collection",
        help="Name of the STAC collection to ingest (e.g., 'sentinel-2-l2a').",
    )
    # No --metadata-url needed anymore
    parser.add_argument(
        "--output-path",
        default=EXT_STAC_TABLE_PATH,
        help=f"Path to the output Delta table. Supports S3 paths (s3://bucket/path) and local paths. Default: {EXT_STAC_TABLE_PATH}",
    )
    parser.add_argument(
        "--bbox", help="Bounding box filter (min_lon,min_lat,max_lon,max_lat)."
    )
    parser.add_argument(
        "--datetime",
        help="Datetime filter (e.g., '2024-01-01/2024-01-31', '2024-01-15T12:00:00Z').",
    )
    parser.add_argument(
        "--limit", type=int, default=100, help="Limit items per STAC API page request."
    )
    parser.add_argument(
        "--max-items",
        type=int,
        default=None,
        help="Max total items to fetch from STAC (default: no limit).",
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=50,
        help="Number of STAC items to process concurrently before writing.",
    )
    parser.add_argument(
        "--max-concurrent",
        type=int,
        default=50,
        help="Max concurrent HTTP requests for header parsing.",
    )
    parser.add_argument(
        "--log-level",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Set logging level.",
    )

    args = parser.parse_args()

    # Update logger level
    logger.setLevel(args.log_level.upper())
    if args.log_level.upper() == "DEBUG":
        logging.getLogger("httpx").setLevel(
            logging.INFO
        )  # Show some httpx logs on debug

    try:
        asyncio.run(main(args, output_path=args.output_path))
    except KeyboardInterrupt:
        logger.info("Ingestion interrupted by user.")
    except Exception as e:
        logger.exception(f"An critical error occurred during ingestion: {e}")
        exit(1)


if __name__ == "__main__":
    main_cli()
