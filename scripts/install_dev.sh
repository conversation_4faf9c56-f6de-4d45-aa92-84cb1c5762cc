#!/bin/bash
# Install all packages in development mode

# Create and activate virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    uv venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install all packages in development mode
echo "Installing packages in development mode..."

# in scripts/install_dev.sh
for lib in \
  libs/tfw_raster_schemas \
  libs/tfw_core_utils \
  libs/tfw_ray_utils \
  libs/tfw_engine_core \
  libs/tfw_processing_api \
  libs/tfw_adapters
do
    uv pip install -e "$lib"        # <- editable!
done

# Install SDK
echo "Installing SDK..."
uv pip install -e sdk

# Install root package (for services)
echo "Installing root package..."
uv pip install -e .

# Install script dependencies
echo "Installing script dependencies..."
uv pip install -r scripts/requirements.txt

# Install dev dependencies
echo "Installing development dependencies..."
uv pip install pytest pytest-cov pytest-mock pytest-asyncio black mypy ruff setuptools

echo "Development environment setup complete!"
