# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Test script to validate S3 catalog connectivity from within Kubernetes environment.
This script can be run inside a processing engine pod to test the cloud catalog setup.
"""

import os
import sys
import logging
from typing import Optional

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_environment_variables():
    """Test that all required environment variables are present."""
    logger.info("🔍 Checking environment variables...")
    
    required_vars = [
        "STAC_CATALOG_S3_BUCKET",
        "STAC_CATALOG_S3_ENDPOINT", 
        "STAC_CATALOG_S3_REGION",
        "DO_ACCESS_KEY_ID",
        "DO_SECRET_ACCESS_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            # Don't log sensitive values
            if "KEY" in var:
                logger.info(f"✅ {var}: [REDACTED]")
            else:
                logger.info(f"✅ {var}: {value}")
        else:
            missing_vars.append(var)
            logger.error(f"❌ {var}: Not set")
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {missing_vars}")
        return False
    
    logger.info("✅ All required environment variables are present")
    return True

def test_s3_imports():
    """Test that S3 dependencies can be imported."""
    logger.info("📦 Testing S3 dependency imports...")
    
    try:
        import boto3
        logger.info("✅ boto3 imported successfully")
    except ImportError as e:
        logger.error(f"❌ Failed to import boto3: {e}")
        return False
    
    try:
        import s3fs
        logger.info("✅ s3fs imported successfully")
    except ImportError as e:
        logger.error(f"❌ Failed to import s3fs: {e}")
        return False
    
    try:
        import deltalake
        logger.info("✅ deltalake imported successfully")
    except ImportError as e:
        logger.error(f"❌ Failed to import deltalake: {e}")
        return False
    
    return True

def test_catalog_client_creation():
    """Test creating a catalog client with S3 backend."""
    logger.info("🏗️ Testing catalog client creation...")
    
    try:
        # Import the updated catalog client
        from terrafloww.engine_core.catalog_client import CatalogClient, S3Config, S3StorageBackend
        logger.info("✅ Catalog client imports successful")
        
        # Create S3 config from environment variables
        s3_config = S3Config(
            bucket=os.environ.get("STAC_CATALOG_S3_BUCKET"),
            region=os.environ.get("STAC_CATALOG_S3_REGION"),
            endpoint_url=os.environ.get("STAC_CATALOG_S3_ENDPOINT"),
            path_prefix=os.environ.get("STAC_CATALOG_S3_PATH_PREFIX", "")
        )
        logger.info("✅ S3 config created successfully")
        
        # Create storage backend
        storage_backend = S3StorageBackend(s3_config)
        logger.info("✅ S3 storage backend created successfully")
        
        # Create catalog client
        catalog_client = CatalogClient(storage_backend=storage_backend)
        logger.info("✅ Catalog client created successfully")
        
        # Test getting catalog path
        catalog_path = catalog_client.catalog_path
        logger.info(f"✅ Catalog path: {catalog_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create catalog client: {e}")
        return False

def test_s3_connectivity():
    """Test basic S3 connectivity."""
    logger.info("🌐 Testing S3 connectivity...")
    
    try:
        import boto3
        from botocore.exceptions import ClientError
        
        # Create S3 client
        s3_client = boto3.client(
            's3',
            aws_access_key_id=os.environ.get("DO_ACCESS_KEY_ID"),
            aws_secret_access_key=os.environ.get("DO_SECRET_ACCESS_KEY"),
            endpoint_url=os.environ.get("STAC_CATALOG_S3_ENDPOINT"),
            region_name=os.environ.get("STAC_CATALOG_S3_REGION")
        )
        
        # Test bucket access
        bucket_name = os.environ.get("STAC_CATALOG_S3_BUCKET")
        s3_client.head_bucket(Bucket=bucket_name)
        logger.info(f"✅ Successfully connected to S3 bucket: {bucket_name}")
        
        return True
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == '404':
            logger.error(f"❌ Bucket {bucket_name} does not exist")
        elif error_code == '403':
            logger.error(f"❌ Access denied to bucket {bucket_name}")
        else:
            logger.error(f"❌ S3 error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Failed to connect to S3: {e}")
        return False

def main():
    """Run all connectivity tests."""
    logger.info("🚀 Starting Kubernetes catalog connectivity tests...")
    
    tests = [
        ("Environment Variables", test_environment_variables),
        ("S3 Imports", test_s3_imports),
        ("Catalog Client Creation", test_catalog_client_creation),
        ("S3 Connectivity", test_s3_connectivity)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: FAILED with exception: {e}")
    
    logger.info(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! S3 catalog connectivity is working.")
        return True
    else:
        logger.error("❌ Some tests failed. Please check the configuration.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
