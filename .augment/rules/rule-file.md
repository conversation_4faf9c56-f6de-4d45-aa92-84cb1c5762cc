---
type: "always_apply"
---

always check for documentation via tools, and always use sequential thinking. always make clean precise changes instead of reinventing the wheel, if errors pop up find root cause and solve that instead of increasing codebase size. follow data oriented programming where its better, and object oriented when necessary, we prioritze for zero copy high throughput efficient simd usage everywhere. never use over enthusiastic language and emojis, be pragmatic and grounded in reality, never overpredict future outcomes