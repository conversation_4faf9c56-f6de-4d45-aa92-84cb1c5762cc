# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Multi-stage Dockerfile for custom Ray image with platform dependencies
# Target: <2GB image size with Ray 2.47.1 base and geospatial dependencies

# Stage 1: Build platform libraries
FROM python:3.12-slim AS builder

# Install UV for fast Python package management (pinned version for reproducibility)
COPY --from=ghcr.io/astral-sh/uv:0.7.13 /uv /uvx /usr/local/bin/

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    build-essential \
    libgdal-dev \
    libproj-dev \
    libgeos-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /build

# Copy platform library source code
COPY libs/tfw_engine_core /build/tfw_engine_core
COPY libs/tfw_ray_utils /build/tfw_ray_utils
COPY libs/tfw_core_utils /build/tfw_core_utils
COPY libs/tfw_raster_schemas /build/tfw_raster_schemas

# Build wheels for platform libraries using UV
RUN --mount=type=cache,target=/root/.cache/uv \
    cd /build/tfw_engine_core && uv build --wheel --out-dir /wheels
RUN --mount=type=cache,target=/root/.cache/uv \
    cd /build/tfw_ray_utils && uv build --wheel --out-dir /wheels
RUN --mount=type=cache,target=/root/.cache/uv \
    cd /build/tfw_core_utils && uv build --wheel --out-dir /wheels
RUN --mount=type=cache,target=/root/.cache/uv \
    cd /build/tfw_raster_schemas && uv build --wheel --out-dir /wheels

# Stage 2: Final Ray image with platform dependencies
FROM rayproject/ray:2.47.1-py312

# Install UV for fast Python package management (pinned version for reproducibility)
COPY --from=ghcr.io/astral-sh/uv:0.7.13 /uv /uvx /usr/local/bin/

# Install system dependencies for geospatial processing
USER root
RUN apt-get update && apt-get install -y \
    libgdal-dev \
    libproj-dev \
    libgeos-dev \
    libspatialindex-dev \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Copy pre-built wheels from builder stage
COPY --from=builder /wheels /tmp/wheels

# Install platform libraries from wheels using UV with cache mount
RUN --mount=type=cache,target=/root/.cache/uv \
    uv pip install --system --no-cache --find-links /tmp/wheels \
    tfw-engine-core \
    tfw-ray-utils \
    tfw-core-utils \
    tfw-raster-schemas

# Install additional geospatial dependencies using UV with cache mount
RUN --mount=type=cache,target=/root/.cache/uv \
    uv pip install --system --no-cache \
    "aiohttp>=3.9.0" \
    "shapely>=2.0" \
    "rasterio>=1.3" \
    "affine>=2.3" \
    "pyproj>=3.0" \
    "imagecodecs>=2023.0.0" \
    "cachetools>=5.0" \
    "pytz"

# Clean up wheels to reduce image size
RUN rm -rf /tmp/wheels

# Set environment variables for Ray
ENV RAY_DISABLE_IMPORT_WARNING=1
ENV RAY_DEDUP_LOGS=0

# Health check for Kubernetes readiness
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD ray status || exit 1

# Switch back to ray user for security
USER ray

# Set working directory
WORKDIR /home/<USER>
