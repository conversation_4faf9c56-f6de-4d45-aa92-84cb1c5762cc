#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Build script for custom Ray Docker image with platform dependencies
# Usage: ./build-ray-image.sh [registry/repo:tag]
# Best Practice: Uses Git SHA + timestamp for immutable image tags

set -e

# Generate immutable image tag based on git SHA + timestamp
GIT_SHA=$(git rev-parse --short HEAD)
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
IMAGE_TAG="${GIT_SHA}-${TIMESTAMP}"

# Default image name with immutable tag
DEFAULT_IMAGE="registry.digitalocean.com/terrafloww-dev/ray-custom:${IMAGE_TAG}"
IMAGE_NAME="${1:-$DEFAULT_IMAGE}"

echo "Building custom Ray image: $IMAGE_NAME"
echo "Git SHA: $GIT_SHA"
echo "Timestamp: $TIMESTAMP"
echo "Image Tag: $IMAGE_TAG"

# Change to repository root
cd "$(dirname "$0")/../.."

# Build the Docker image
echo "Building Docker image..."
docker build \
    -f infra/k8s/ray-custom.Dockerfile \
    -t "$IMAGE_NAME" \
    .

echo "Image built successfully: $IMAGE_NAME"

# Check image size
echo "Image size:"
docker images "$IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"

# Optional: Push to registry if requested
if [[ "${PUSH_IMAGE:-false}" == "true" ]]; then
    echo "Pushing image to registry..."
    docker push "$IMAGE_NAME"
    echo "Image pushed successfully!"
fi

echo "Build complete!"
echo ""
echo "Next steps:"
echo "1. To push the image, run: PUSH_IMAGE=true $0 $IMAGE_NAME"
echo "2. To use in Kubernetes, update your RayCluster manifest to use: $IMAGE_NAME"
echo "3. Update ray-cluster.yaml with the new image tag: $IMAGE_TAG"
echo ""
echo "Example deployment command:"
echo "  sed -i 's|ray-custom:.*|ray-custom:$IMAGE_TAG|g' infra/k8s/ray-cluster.yaml"
echo "  kubectl delete raycluster terrafloww-ray-cluster -n terrafloww-platform"
echo "  kubectl apply -f infra/k8s/ray-cluster.yaml"
