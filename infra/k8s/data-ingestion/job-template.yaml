# SPDX-FileCopyrightText: Terrafloww Labs, 2025

apiVersion: batch/v1
kind: Job
metadata:
  name: data-ingestion-${PLUGIN}-${TIMESTAMP}
  namespace: terrafloww-platform
  labels:
    app: data-ingestion
    plugin: ${PLUGIN}
    version: v1
spec:
  ttlSecondsAfterFinished: 3600  # Clean up after 1 hour
  backoffLimit: 3
  template:
    metadata:
      labels:
        app: data-ingestion
        plugin: ${PLUGIN}
    spec:
      serviceAccountName: data-ingestion
      restartPolicy: Never
      imagePullSecrets:
      - name: terrafloww-dev
      containers:
      - name: ingestion
        image: ${REGISTRY}/data-ingestion:${VERSION}
        imagePullPolicy: Always
        command: ["uv", "run", "python", "main.py"]
        args: ["${PLUGIN}"]
        env:
        # S3 Configuration
        - name: STAC_CATALOG_S3_BUCKET
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: bucket
        - name: STAC_CATALOG_S3_ENDPOINT
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: endpoint
        - name: STAC_CATALOG_S3_REGION
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: region
        - name: STAC_CATALOG_S3_PATH_PREFIX
          value: "catalog"
        
        # AWS Credentials
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: access_key_id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: secret_access_key
        - name: AWS_REGION
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: region
        
        # Plugin Parameters (to be substituted)
        - name: PLUGIN_PARAMETERS
          value: '${PLUGIN_PARAMETERS}'
        
        # Logging
        - name: LOG_LEVEL
          value: "${LOG_LEVEL:-INFO}"
        
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        
        # Health checks
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "ps aux | grep '[p]ython main.py' || exit 1"
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3

---
# Example STAC ingestion job
apiVersion: batch/v1
kind: Job
metadata:
  name: stac-ingestion-sentinel2-example
  namespace: terrafloww-platform
  labels:
    app: data-ingestion
    plugin: stac
    collection: sentinel-2-l2a
spec:
  ttlSecondsAfterFinished: 3600
  backoffLimit: 3
  template:
    metadata:
      labels:
        app: data-ingestion
        plugin: stac
    spec:
      serviceAccountName: data-ingestion
      restartPolicy: Never
      imagePullSecrets:
      - name: terrafloww-dev
      containers:
      - name: ingestion
        image: registry.digitalocean.com/terrafloww-dev/data-ingestion:latest
        imagePullPolicy: Always
        command: ["uv", "run", "python", "main.py"]
        args: 
        - "stac"
        - "--collection=sentinel-2-l2a"
        - "--bbox=-122.5,37.7,-122.3,37.9"
        - "--datetime=2024-06-26/2024-07-25"
        - "--max-items=100"
        - "--batch-size=10"
        - "--log-level=INFO"
        env:
        # S3 Configuration
        - name: STAC_CATALOG_S3_BUCKET
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: bucket
        - name: STAC_CATALOG_S3_ENDPOINT
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: endpoint
        - name: STAC_CATALOG_S3_REGION
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: region
        - name: STAC_CATALOG_S3_PATH_PREFIX
          value: "catalog"
        
        # AWS Credentials
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: access_key_id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: secret_access_key
        - name: AWS_REGION
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: region
        
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
