# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# GPU-enabled Ray cluster configuration for Phase 2 GPU-direct pipeline
# This configuration adds GPU worker groups for inference workloads

apiVersion: ray.io/v1alpha1
kind: RayCluster
metadata:
  name: terrafloww-ray-cluster-gpu
  labels:
    app: terrafloww
    component: ray-cluster-gpu
spec:
  # Ray version for GPU object support (Phase 2)
  rayVersion: '2.48.0'
  
  # Enable autoscaling for dynamic worker scaling
  enableInTreeAutoscaling: true
  
  # Head node configuration (same as CPU-only cluster)
  headGroupSpec:
    serviceType: ClusterIP
    rayStartParams:
      dashboard-host: '0.0.0.0'
      dashboard-port: '8265'
      object-store-memory: '1073741824'  # 1GB object store for GPU data
      num-cpus: '0'  # Head node doesn't run workloads
      block: 'true'
    template:
      metadata:
        labels:
          app: terrafloww
          component: ray-head-gpu
      spec:
        # Schedule Ray head on data pool nodes
        nodeSelector:
          doks.digitalocean.com/node-pool: data-pool-e0y1e6npd
        tolerations:
        - key: workload
          operator: Equal
          value: data
          effect: NoSchedule
        imagePullSecrets:
        - name: registry-terrafloww-dev
        containers:
        - name: ray-head
          image: registry.digitalocean.com/terrafloww-dev/ray-custom:gpu-latest
          imagePullPolicy: Always
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "ray stop"]
          ports:
          - containerPort: 6379  # GCS server
            name: gcs-server
          - containerPort: 8265  # Dashboard
            name: dashboard
          - containerPort: 10001  # Ray client
            name: client
          resources:
            limits:
              cpu: "2"
              memory: "8Gi"  # Increased for GPU coordination
            requests:
              cpu: "1"
              memory: "4Gi"
          readinessProbe:
            exec:
              command:
              - bash
              - -c
              - wget -T 4 -q -O- http://localhost:52365/api/local_raylet_healthz | grep success &&
                wget -T 10 -q -O- http://localhost:8265/api/gcs_healthz | grep success
            periodSeconds: 5
            timeoutSeconds: 10
            failureThreshold: 12
          env:
          - name: RAY_DISABLE_IMPORT_WARNING
            value: "1"
          - name: RAY_DEDUP_LOGS
            value: "0"
          - name: RAY_DASHBOARD_METRICS_COLLECTION_ENABLED
            value: "1"  # Enable metrics for GPU monitoring
          - name: RAY_USAGE_STATS_ENABLED
            value: "0"
          # Phase 1 optimizations
          - name: TFW_USE_HTTP_POOL
            value: "true"
          - name: TFW_CHUNKY_BATCHING_ENABLED
            value: "true"
          - name: TFW_HILBERT_SORTING_ENABLED
            value: "true"
          # Phase 2 GPU configuration
          - name: TFW_GPU_PIPELINE_ENABLED
            value: "true"
          - name: TFW_GPU_INFERENCE_ENABLED
            value: "true"
          - name: FLIGHT_HOST
            value: "************"
          - name: FLIGHT_PORT
            value: "50052"
          volumeMounts:
          - mountPath: /tmp/ray
            name: ray-logs
          - mountPath: /dev/shm
            name: shared-mem
        volumes:
        - name: ray-logs
          emptyDir: {}
        - name: shared-mem
          emptyDir:
            medium: Memory
            sizeLimit: 4Gi

  # Worker group specifications
  workerGroupSpecs:
  # CPU worker group for data loading and preprocessing
  - replicas: 2
    minReplicas: 1
    maxReplicas: 8
    groupName: cpu-worker-group
    rayStartParams:
      num-cpus: '4'
      block: 'true'
    template:
      metadata:
        labels:
          app: terrafloww
          component: ray-worker-cpu
      spec:
        nodeSelector:
          doks.digitalocean.com/node-pool: data-pool-e0y1e6npd
        tolerations:
        - key: workload
          operator: Equal
          value: data
          effect: NoSchedule
        imagePullSecrets:
        - name: registry-terrafloww-dev
        containers:
        - name: ray-worker
          image: registry.digitalocean.com/terrafloww-dev/ray-custom:gpu-latest
          imagePullPolicy: Always
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "ray stop"]
          resources:
            limits:
              cpu: "4"
              memory: "8Gi"
            requests:
              cpu: "2"
              memory: "4Gi"
          env:
          - name: RAY_DISABLE_IMPORT_WARNING
            value: "1"
          - name: RAY_DEDUP_LOGS
            value: "0"
          - name: RAY_USAGE_STATS_ENABLED
            value: "0"
          # Phase 1 optimizations
          - name: TFW_USE_HTTP_POOL
            value: "true"
          - name: TFW_CHUNKY_BATCHING_ENABLED
            value: "true"
          - name: TFW_HILBERT_SORTING_ENABLED
            value: "true"
          # Phase 2 GPU configuration
          - name: TFW_GPU_PIPELINE_ENABLED
            value: "true"
          - name: FLIGHT_HOST
            value: "terrafloww-processing-engine-svc"
          - name: FLIGHT_PORT
            value: "50052"
          volumeMounts:
          - mountPath: /tmp/ray
            name: ray-logs
          - mountPath: /dev/shm
            name: shared-mem
        volumes:
        - name: ray-logs
          emptyDir: {}
        - name: shared-mem
          emptyDir:
            medium: Memory
            sizeLimit: 4Gi
        restartPolicy: Never

  # GPU worker group for inference (Phase 2)
  - replicas: 1
    minReplicas: 0
    maxReplicas: 4
    groupName: gpu-worker-group
    rayStartParams:
      num-cpus: '2'
      num-gpus: '1'
      block: 'true'
    template:
      metadata:
        labels:
          app: terrafloww
          component: ray-worker-gpu
      spec:
        # Schedule on GPU-enabled nodes (when available)
        nodeSelector:
          accelerator: nvidia-tesla-t4  # Adjust based on available GPU nodes
        tolerations:
        - key: nvidia.com/gpu
          operator: Exists
          effect: NoSchedule
        imagePullSecrets:
        - name: registry-terrafloww-dev
        containers:
        - name: ray-worker
          image: registry.digitalocean.com/terrafloww-dev/ray-custom:gpu-latest
          imagePullPolicy: Always
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "ray stop"]
          resources:
            limits:
              cpu: "4"
              memory: "16Gi"
              nvidia.com/gpu: "1"
            requests:
              cpu: "2"
              memory: "8Gi"
              nvidia.com/gpu: "1"
          env:
          - name: RAY_DISABLE_IMPORT_WARNING
            value: "1"
          - name: RAY_DEDUP_LOGS
            value: "0"
          - name: RAY_USAGE_STATS_ENABLED
            value: "0"
          # CUDA configuration
          - name: CUDA_VISIBLE_DEVICES
            value: "0"
          - name: NVIDIA_VISIBLE_DEVICES
            value: "all"
          - name: NVIDIA_DRIVER_CAPABILITIES
            value: "compute,utility"
          # Phase 1 optimizations
          - name: TFW_USE_HTTP_POOL
            value: "true"
          - name: TFW_CHUNKY_BATCHING_ENABLED
            value: "true"
          - name: TFW_HILBERT_SORTING_ENABLED
            value: "true"
          # Phase 2 GPU configuration
          - name: TFW_GPU_PIPELINE_ENABLED
            value: "true"
          - name: TFW_GPU_INFERENCE_ENABLED
            value: "true"
          - name: TFW_GPU_INFERENCE_MODEL_PATH
            value: "/models/inference_model.pt"
          - name: FLIGHT_HOST
            value: "terrafloww-processing-engine-svc"
          - name: FLIGHT_PORT
            value: "50052"
          volumeMounts:
          - mountPath: /tmp/ray
            name: ray-logs
          - mountPath: /dev/shm
            name: shared-mem
          - mountPath: /models
            name: model-storage
        volumes:
        - name: ray-logs
          emptyDir: {}
        - name: shared-mem
          emptyDir:
            medium: Memory
            sizeLimit: 8Gi  # Larger shared memory for GPU data
        - name: model-storage
          persistentVolumeClaim:
            claimName: model-storage-pvc
        restartPolicy: Never
