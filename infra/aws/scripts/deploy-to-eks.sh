#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# Deploy Terrafloww Platform to AWS EKS

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR/../../../"
AWS_REGION="${AWS_REGION:-us-west-2}"
CLUSTER_NAME="${CLUSTER_NAME:-terrafloww-eks-cluster}"
NAMESPACE="terrafloww-platform"

# Get AWS account ID and ECR registry
ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
ECR_REGISTRY="${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"

# Generate immutable image tag based on git SHA + timestamp
GIT_SHA=$(git rev-parse --short HEAD)
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
IMAGE_TAG="${GIT_SHA}-${TIMESTAMP}"

# Component to deploy
COMPONENT="${1:-all}"

echo -e "${BLUE}🚀 Terrafloww Platform Deployment to EKS${NC}"
echo "============================================="
echo -e "AWS Region: ${GREEN}$AWS_REGION${NC}"
echo -e "EKS Cluster: ${GREEN}$CLUSTER_NAME${NC}"
echo -e "ECR Registry: ${GREEN}$ECR_REGISTRY${NC}"
echo -e "Git SHA: ${GREEN}$GIT_SHA${NC}"
echo -e "Timestamp: ${GREEN}$TIMESTAMP${NC}"
echo -e "Image Tag: ${GREEN}$IMAGE_TAG${NC}"
echo -e "Component: ${GREEN}$COMPONENT${NC}"
echo -e "Namespace: ${GREEN}$NAMESPACE${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if kubectl is configured for EKS
    if ! kubectl cluster-info >/dev/null 2>&1; then
        print_error "kubectl not configured for EKS cluster"
        echo "Run: aws eks --region $AWS_REGION update-kubeconfig --name $CLUSTER_NAME"
        exit 1
    fi
    
    # Check if we can access ECR
    if ! aws ecr describe-repositories --region "$AWS_REGION" >/dev/null 2>&1; then
        print_error "Cannot access ECR repositories"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# ECR login
ecr_login() {
    print_status "Logging into ECR..."
    
    aws ecr get-login-password --region "$AWS_REGION" | docker login --username AWS --password-stdin "$ECR_REGISTRY"
    
    print_success "ECR login successful"
}

# Build and push Ray custom image
build_ray_image() {
    print_status "Building and pushing Ray custom image..."
    
    cd "$PROJECT_ROOT"
    
    local ray_image="$ECR_REGISTRY/terrafloww/ray-custom:$IMAGE_TAG"
    
    # Build Ray custom image
    docker build -f infra/k8s/ray-custom.Dockerfile -t "$ray_image" .
    
    # Push to ECR
    docker push "$ray_image"
    
    print_success "Ray custom image built and pushed: $ray_image"
    echo "RAY_IMAGE=$ray_image" > /tmp/terrafloww-ray-image.env
}

# Build and push Processing Engine image
build_processing_engine_image() {
    print_status "Building and pushing Processing Engine image..."
    
    cd "$PROJECT_ROOT"
    
    local pe_image="$ECR_REGISTRY/terrafloww/processing-engine:$IMAGE_TAG"
    
    # Build Processing Engine image
    docker build -f services/processing_engine/Dockerfile -t "$pe_image" .
    
    # Push to ECR
    docker push "$pe_image"
    
    print_success "Processing Engine image built and pushed: $pe_image"
    echo "PE_IMAGE=$pe_image" > /tmp/terrafloww-pe-image.env
}

# Create namespace
create_namespace() {
    print_status "Creating namespace..."
    
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
    
    print_success "Namespace $NAMESPACE created/updated"
}

# Deploy secrets
deploy_secrets() {
    print_status "Deploying secrets..."
    
    # Apply AWS-specific secrets
    kubectl apply -f "$SCRIPT_DIR/../k8s/aws-secrets.yaml" -n "$NAMESPACE"
    
    print_success "Secrets deployed"
}

# Deploy Ray cluster
deploy_ray() {
    print_status "Deploying Ray cluster..."
    
    # Source the Ray image environment
    source /tmp/terrafloww-ray-image.env
    
    # Create temporary Ray cluster manifest with updated image
    local temp_ray_manifest="/tmp/ray-cluster-aws.yaml"
    cp "$SCRIPT_DIR/../k8s/ray-cluster-aws.yaml" "$temp_ray_manifest"
    
    # Update image in the manifest
    sed -i "s|{{RAY_IMAGE}}|$RAY_IMAGE|g" "$temp_ray_manifest"
    
    # Delete existing Ray cluster if it exists
    kubectl delete raycluster terrafloww-ray-cluster -n "$NAMESPACE" --ignore-not-found=true
    
    # Wait a bit for cleanup
    sleep 10
    
    # Deploy Ray cluster
    kubectl apply -f "$temp_ray_manifest" -n "$NAMESPACE"
    
    # Wait for Ray cluster to be ready
    print_status "Waiting for Ray cluster to be ready..."
    kubectl wait --for=condition=ready pod -l ray.io/cluster=terrafloww-ray-cluster -n "$NAMESPACE" --timeout=600s
    
    print_success "Ray cluster deployed successfully"
    
    # Cleanup temp file
    rm -f "$temp_ray_manifest"
}

# Deploy Processing Engine
deploy_processing_engine() {
    print_status "Deploying Processing Engine..."
    
    # Source the Processing Engine image environment
    source /tmp/terrafloww-pe-image.env
    
    # Create temporary Processing Engine manifest with updated image
    local temp_pe_manifest="/tmp/processing-engine-aws.yaml"
    cp "$SCRIPT_DIR/../k8s/processing-engine-aws.yaml" "$temp_pe_manifest"
    
    # Update image in the manifest
    sed -i "s|{{PE_IMAGE}}|$PE_IMAGE|g" "$temp_pe_manifest"
    
    # Deploy Processing Engine
    kubectl apply -f "$temp_pe_manifest" -n "$NAMESPACE"
    
    # Wait for Processing Engine to be ready
    print_status "Waiting for Processing Engine to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/terrafloww-processing-engine -n "$NAMESPACE"
    
    print_success "Processing Engine deployed successfully"
    
    # Cleanup temp file
    rm -f "$temp_pe_manifest"
}

# Verify deployment
verify_deployment() {
    print_status "Verifying deployment..."
    
    echo ""
    echo "Pods in namespace $NAMESPACE:"
    kubectl get pods -n "$NAMESPACE"
    
    echo ""
    echo "Services in namespace $NAMESPACE:"
    kubectl get services -n "$NAMESPACE"
    
    echo ""
    echo "Ray cluster status:"
    kubectl get raycluster -n "$NAMESPACE"
    
    # Check if all pods are running
    local not_running=$(kubectl get pods -n "$NAMESPACE" --field-selector=status.phase!=Running --no-headers | wc -l)
    
    if [ "$not_running" -eq 0 ]; then
        print_success "All pods are running"
    else
        print_warning "$not_running pods are not in Running state"
        kubectl get pods -n "$NAMESPACE" --field-selector=status.phase!=Running
    fi
}

# Display connection information
display_connection_info() {
    echo ""
    echo -e "${GREEN}🎉 Deployment Complete!${NC}"
    echo "========================"
    echo ""
    echo "Connection Information:"
    echo ""
    
    # Get Processing Engine service details
    local pe_service=$(kubectl get service terrafloww-processing-engine-svc -n "$NAMESPACE" -o jsonpath='{.spec.clusterIP}')
    local pe_port=$(kubectl get service terrafloww-processing-engine-svc -n "$NAMESPACE" -o jsonpath='{.spec.ports[0].port}')
    
    echo -e "Processing Engine Service: ${GREEN}$pe_service:$pe_port${NC}"
    echo ""
    echo "Port Forward Commands:"
    echo -e "  ${BLUE}kubectl port-forward -n $NAMESPACE svc/terrafloww-processing-engine-svc 50051:50051${NC}"
    echo -e "  ${BLUE}kubectl port-forward -n $NAMESPACE svc/terrafloww-ray-cluster-head-svc 8265:8265${NC}"
    echo ""
    echo "Test Commands:"
    echo -e "  ${BLUE}cd ../../../terrafloww-sdk-public${NC}"
    echo -e "  ${BLUE}python tests/test_basic.py${NC}"
    echo ""
    echo "Monitoring Commands:"
    echo -e "  ${BLUE}kubectl logs -n $NAMESPACE -l app=terrafloww,component=processing-engine --tail=100${NC}"
    echo -e "  ${BLUE}kubectl logs -n $NAMESPACE -l ray.io/node-type=head --tail=100${NC}"
    echo -e "  ${BLUE}kubectl get pods -n $NAMESPACE -w${NC}"
    echo ""
}

# Cleanup function
cleanup() {
    print_status "Cleaning up temporary files..."
    rm -f /tmp/terrafloww-ray-image.env
    rm -f /tmp/terrafloww-pe-image.env
    rm -f /tmp/ray-cluster-aws.yaml
    rm -f /tmp/processing-engine-aws.yaml
}

# Main deployment function
deploy_all() {
    create_namespace
    deploy_secrets
    build_ray_image
    build_processing_engine_image
    deploy_ray
    deploy_processing_engine
    verify_deployment
    display_connection_info
}

# Main execution
main() {
    case "$COMPONENT" in
        "ray")
            check_prerequisites
            ecr_login
            create_namespace
            build_ray_image
            deploy_ray
            verify_deployment
            ;;
        "processing-engine")
            check_prerequisites
            ecr_login
            create_namespace
            deploy_secrets
            build_processing_engine_image
            deploy_processing_engine
            verify_deployment
            ;;
        "all")
            check_prerequisites
            ecr_login
            deploy_all
            ;;
        *)
            print_error "Invalid component: $COMPONENT"
            echo "Usage: $0 [ray|processing-engine|all]"
            exit 1
            ;;
    esac
    
    cleanup
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Change to project root
cd "$PROJECT_ROOT"

# Run main function
main "$@"
