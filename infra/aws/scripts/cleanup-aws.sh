#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# Cleanup AWS resources for Terrafloww Platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TERRAFORM_DIR="$SCRIPT_DIR/../terraform"
AWS_REGION="${AWS_REGION:-us-west-2}"
CLUSTER_NAME="${CLUSTER_NAME:-terrafloww-eks-cluster}"
NAMESPACE="terrafloww-platform"

echo -e "${YELLOW}⚠️  Terrafloww AWS Cleanup${NC}"
echo "=========================="
echo -e "AWS Region: ${RED}$AWS_REGION${NC}"
echo -e "EKS Cluster: ${RED}$CLUSTER_NAME${NC}"
echo -e "Namespace: ${RED}$NAMESPACE${NC}"
echo ""
echo -e "${RED}WARNING: This will delete ALL AWS resources created for Terrafloww!${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Confirmation prompt
confirm_cleanup() {
    echo -e "${YELLOW}This will permanently delete:${NC}"
    echo "  - EKS cluster and all workloads"
    echo "  - ECR repositories and all images"
    echo "  - VPC and networking resources"
    echo "  - IAM roles and policies"
    echo "  - All data and configurations"
    echo ""
    echo -e "${RED}This action cannot be undone!${NC}"
    echo ""
    
    read -p "Are you absolutely sure you want to proceed? (type 'DELETE' to confirm): " confirmation
    
    if [ "$confirmation" != "DELETE" ]; then
        print_warning "Cleanup cancelled by user"
        exit 0
    fi
    
    echo ""
    print_warning "Starting cleanup in 10 seconds... Press Ctrl+C to cancel"
    sleep 10
}

# Check if kubectl is configured
check_kubectl() {
    if kubectl cluster-info >/dev/null 2>&1; then
        print_status "kubectl is configured for cluster"
        return 0
    else
        print_warning "kubectl not configured for EKS cluster"
        return 1
    fi
}

# Delete Kubernetes resources
cleanup_kubernetes() {
    print_status "Cleaning up Kubernetes resources..."
    
    if check_kubectl; then
        # Delete namespace (this will delete all resources in the namespace)
        kubectl delete namespace "$NAMESPACE" --ignore-not-found=true --timeout=300s
        
        # Delete KubeRay operator
        helm uninstall kuberay-operator -n kuberay-operator --ignore-not-found
        kubectl delete namespace kuberay-operator --ignore-not-found=true
        
        print_success "Kubernetes resources cleaned up"
    else
        print_warning "Skipping Kubernetes cleanup (cluster not accessible)"
    fi
}

# Delete ECR images
cleanup_ecr_images() {
    print_status "Cleaning up ECR images..."
    
    local repositories=("terrafloww/ray-custom" "terrafloww/processing-engine")
    
    for repo in "${repositories[@]}"; do
        if aws ecr describe-repositories --repository-names "$repo" --region "$AWS_REGION" >/dev/null 2>&1; then
            print_status "Deleting images in repository: $repo"
            
            # Get all image tags
            local image_tags=$(aws ecr list-images --repository-name "$repo" --region "$AWS_REGION" --query 'imageIds[*].imageTag' --output text)
            
            if [ -n "$image_tags" ]; then
                # Delete all images
                aws ecr batch-delete-image \
                    --repository-name "$repo" \
                    --region "$AWS_REGION" \
                    --image-ids $(echo "$image_tags" | tr ' ' '\n' | sed 's/^/imageTag=/' | tr '\n' ' ') \
                    >/dev/null 2>&1 || true
                
                print_success "Images deleted from repository: $repo"
            else
                print_status "No images found in repository: $repo"
            fi
        else
            print_warning "Repository not found: $repo"
        fi
    done
}

# Terraform cleanup
cleanup_terraform() {
    print_status "Cleaning up infrastructure with Terraform..."
    
    cd "$TERRAFORM_DIR"
    
    if [ ! -f "terraform.tfstate" ]; then
        print_warning "No Terraform state found, skipping Terraform cleanup"
        return 0
    fi
    
    # Initialize Terraform
    terraform init
    
    # Plan destroy
    print_status "Planning Terraform destroy..."
    terraform plan -destroy -out=destroy.tfplan
    
    # Show what will be destroyed
    echo ""
    echo -e "${YELLOW}Terraform will destroy the following resources:${NC}"
    terraform show destroy.tfplan
    echo ""
    
    read -p "Proceed with Terraform destroy? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # Apply destroy
        print_status "Destroying infrastructure..."
        terraform apply destroy.tfplan
        
        print_success "Infrastructure destroyed"
        
        # Clean up Terraform files
        rm -f destroy.tfplan
        rm -f terraform.tfstate.backup
    else
        print_warning "Terraform destroy cancelled"
        rm -f destroy.tfplan
    fi
}

# Manual cleanup for any remaining resources
manual_cleanup() {
    print_status "Checking for any remaining resources..."
    
    # Check for any remaining EKS clusters
    local clusters=$(aws eks list-clusters --region "$AWS_REGION" --query "clusters[?contains(@, '$CLUSTER_NAME')]" --output text)
    if [ -n "$clusters" ]; then
        print_warning "Found remaining EKS clusters: $clusters"
        echo "You may need to delete these manually:"
        echo "  aws eks delete-cluster --name $CLUSTER_NAME --region $AWS_REGION"
    fi
    
    # Check for any remaining ECR repositories
    local repos=$(aws ecr describe-repositories --region "$AWS_REGION" --query "repositories[?contains(repositoryName, 'terrafloww')].repositoryName" --output text 2>/dev/null || true)
    if [ -n "$repos" ]; then
        print_warning "Found remaining ECR repositories: $repos"
        echo "You may need to delete these manually:"
        for repo in $repos; do
            echo "  aws ecr delete-repository --repository-name $repo --region $AWS_REGION --force"
        done
    fi
    
    # Check for any remaining VPCs with terrafloww tag
    local vpcs=$(aws ec2 describe-vpcs --region "$AWS_REGION" --filters "Name=tag:Project,Values=Terrafloww" --query "Vpcs[].VpcId" --output text 2>/dev/null || true)
    if [ -n "$vpcs" ]; then
        print_warning "Found remaining VPCs: $vpcs"
        echo "These should have been deleted by Terraform. Check for dependencies."
    fi
}

# Display cleanup summary
display_summary() {
    echo ""
    echo -e "${GREEN}🧹 Cleanup Summary${NC}"
    echo "=================="
    echo ""
    echo "Cleaned up:"
    echo "  ✅ Kubernetes resources and namespaces"
    echo "  ✅ ECR repositories and images"
    echo "  ✅ EKS cluster and node groups"
    echo "  ✅ VPC and networking resources"
    echo "  ✅ IAM roles and policies"
    echo ""
    echo "Next steps:"
    echo "  1. Verify no unexpected charges in AWS billing"
    echo "  2. Check for any remaining resources manually"
    echo "  3. Remove local Terraform state if desired:"
    echo "     rm -rf $TERRAFORM_DIR/.terraform"
    echo "     rm -f $TERRAFORM_DIR/terraform.tfstate*"
    echo ""
    echo -e "${GREEN}Cleanup completed successfully!${NC}"
}

# Main cleanup function
main() {
    confirm_cleanup
    cleanup_kubernetes
    cleanup_ecr_images
    cleanup_terraform
    manual_cleanup
    display_summary
}

# Run main function
main "$@"
