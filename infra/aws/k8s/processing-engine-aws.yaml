# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# Processing Engine deployment for AWS EKS

apiVersion: apps/v1
kind: Deployment
metadata:
  name: terrafloww-processing-engine
  namespace: terrafloww-platform
  labels:
    app: terrafloww
    component: processing-engine
spec:
  replicas: 1
  selector:
    matchLabels:
      app: terrafloww
      component: processing-engine
  template:
    metadata:
      labels:
        app: terrafloww
        component: processing-engine
    spec:
      containers:
      - name: processing-engine
        image: {{PE_IMAGE}}  # Will be replaced by deployment script
        imagePullPolicy: Always
        ports:
        - containerPort: 50051
          name: grpc
        - containerPort: 50052
          name: flight
        resources:
          limits:
            cpu: "2"
            memory: "4Gi"
          requests:
            cpu: "1"
            memory: "2Gi"
        env:
        # Ray cluster connection
        - name: RAY_ADDRESS
          value: "ray://terrafloww-ray-cluster-head-svc:10001"
        
        # Flight server configuration
        - name: FLIGHT_HOST
          value: "0.0.0.0"
        - name: FLIGHT_PORT
          value: "50052"
        - name: FLIGHT_INTERNAL_HOST
          value: "terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local"
        - name: FLIGHT_INTERNAL_PORT
          value: "50052"
        
        # gRPC server configuration
        - name: GRPC_HOST
          value: "0.0.0.0"
        - name: GRPC_PORT
          value: "50051"
        
        # AWS-specific configuration
        - name: AWS_DEFAULT_REGION
          value: "us-west-2"
        - name: AWS_REGION
          value: "us-west-2"
        
        # HTTP optimization
        - name: TFW_USE_HTTP_POOL
          value: "true"
        
        # Catalog configuration (from secrets)
        - name: CATALOG_BUCKET
          valueFrom:
            secretKeyRef:
              name: catalog-secrets
              key: bucket
        - name: CATALOG_ENDPOINT
          valueFrom:
            secretKeyRef:
              name: catalog-secrets
              key: endpoint
        - name: CATALOG_REGION
          valueFrom:
            secretKeyRef:
              name: catalog-secrets
              key: region
        - name: CATALOG_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: catalog-secrets
              key: access_key_id
        - name: CATALOG_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: catalog-secrets
              key: secret_access_key
        
        # Logging configuration
        - name: LOG_LEVEL
          value: "INFO"
        - name: PYTHONUNBUFFERED
          value: "1"
        
        livenessProbe:
          exec:
            command:
            - python
            - -c
            - "import grpc; import sys; channel = grpc.insecure_channel('localhost:50051'); sys.exit(0 if channel else 1)"
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        
        readinessProbe:
          exec:
            command:
            - python
            - -c
            - "import grpc; import sys; channel = grpc.insecure_channel('localhost:50051'); sys.exit(0 if channel else 1)"
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        volumeMounts:
        - name: tmp-volume
          mountPath: /tmp
        - name: cache-volume
          mountPath: /app/cache
      
      volumes:
      - name: tmp-volume
        emptyDir: {}
      - name: cache-volume
        emptyDir:
          sizeLimit: 10Gi
      
      nodeSelector:
        kubernetes.io/arch: amd64
      
      # Prefer to run on different nodes than Ray workers for better resource distribution
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: ray.io/node-type
                  operator: In
                  values: ["worker"]
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: terrafloww-processing-engine-svc
  namespace: terrafloww-platform
  labels:
    app: terrafloww
    component: processing-engine-service
spec:
  type: ClusterIP
  selector:
    app: terrafloww
    component: processing-engine
  ports:
  - name: grpc
    port: 50051
    targetPort: 50051
    protocol: TCP
  - name: flight
    port: 50052
    targetPort: 50052
    protocol: TCP
---
# Optional: External LoadBalancer service for Processing Engine
apiVersion: v1
kind: Service
metadata:
  name: terrafloww-processing-engine-external
  namespace: terrafloww-platform
  labels:
    app: terrafloww
    component: processing-engine-external
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-scheme: "internet-facing"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
spec:
  type: LoadBalancer
  selector:
    app: terrafloww
    component: processing-engine
  ports:
  - name: grpc
    port: 50051
    targetPort: 50051
    protocol: TCP
  - name: flight
    port: 50052
    targetPort: 50052
    protocol: TCP
