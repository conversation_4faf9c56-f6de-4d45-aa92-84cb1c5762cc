# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# AWS-specific secrets for Terrafloww Platform

apiVersion: v1
kind: Secret
metadata:
  name: catalog-secrets
  namespace: terrafloww-platform
  labels:
    app: terrafloww
    component: secrets
type: Opaque
data:
  # AWS S3 bucket for STAC catalog storage
  # Replace with base64 encoded values:
  # echo -n "your-bucket-name" | base64
  bucket: dGVycmFmbG93dy1zdGFjLWNhdGFsb2c=  # terrafloww-stac-catalog
  
  # AWS S3 endpoint (for S3, this is typically the regional endpoint)
  # echo -n "s3.us-west-2.amazonaws.com" | base64
  endpoint: czMudXMtd2VzdC0yLmFtYXpvbmF3cy5jb20=  # s3.us-west-2.amazonaws.com
  
  # AWS region
  # echo -n "us-west-2" | base64
  region: dXMtd2VzdC0y  # us-west-2
  
  # AWS Access Key ID
  # echo -n "YOUR_ACCESS_KEY_ID" | base64
  access_key_id: WU9VUl9BQ0NFU1NfS0VZX0lE  # YOUR_ACCESS_KEY_ID (placeholder)
  
  # AWS Secret Access Key
  # echo -n "YOUR_SECRET_ACCESS_KEY" | base64
  secret_access_key: WU9VUl9TRUNSRVRfQUNDRVNTX0tFWQ==  # YOUR_SECRET_ACCESS_KEY (placeholder)

---
# Optional: AWS credentials for ECR access (if using IRSA, this may not be needed)
apiVersion: v1
kind: Secret
metadata:
  name: aws-ecr-credentials
  namespace: terrafloww-platform
  labels:
    app: terrafloww
    component: ecr-secrets
type: kubernetes.io/dockerconfigjson
data:
  # Docker config for ECR access
  # This will be generated automatically by the deployment script
  # Format: {"auths":{"123456789012.dkr.ecr.us-west-2.amazonaws.com":{"username":"AWS","password":"TOKEN"}}}
  .dockerconfigjson: e30=  # {} (empty placeholder, will be updated by script)

---
# ConfigMap for AWS-specific configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: aws-config
  namespace: terrafloww-platform
  labels:
    app: terrafloww
    component: config
data:
  # AWS region
  AWS_DEFAULT_REGION: "us-west-2"
  AWS_REGION: "us-west-2"
  
  # S3 configuration
  S3_ENDPOINT: "s3.us-west-2.amazonaws.com"
  S3_USE_SSL: "true"
  S3_VERIFY_SSL: "true"
  
  # Ray configuration for AWS
  RAY_USAGE_STATS_ENABLED: "0"
  RAY_DISABLE_IMPORT_WARNING: "1"
  RAY_DEDUP_LOGS: "0"
  
  # HTTP optimization
  TFW_USE_HTTP_POOL: "true"
  
  # Logging
  LOG_LEVEL: "INFO"
  PYTHONUNBUFFERED: "1"

---
# Instructions for updating secrets
apiVersion: v1
kind: ConfigMap
metadata:
  name: secrets-instructions
  namespace: terrafloww-platform
  labels:
    app: terrafloww
    component: instructions
data:
  README.md: |
    # AWS Secrets Configuration
    
    ## Required Updates
    
    Before deploying, you must update the following secrets with your actual AWS credentials:
    
    ### 1. Update S3 Bucket Name
    ```bash
    # Replace 'terrafloww-stac-catalog' with your actual bucket name
    kubectl patch secret catalog-secrets -n terrafloww-platform -p='{"data":{"bucket":"'$(echo -n "your-actual-bucket-name" | base64)'"}}'
    ```
    
    ### 2. Update AWS Credentials
    ```bash
    # Update access key ID
    kubectl patch secret catalog-secrets -n terrafloww-platform -p='{"data":{"access_key_id":"'$(echo -n "YOUR_ACTUAL_ACCESS_KEY_ID" | base64)'"}}'
    
    # Update secret access key
    kubectl patch secret catalog-secrets -n terrafloww-platform -p='{"data":{"secret_access_key":"'$(echo -n "YOUR_ACTUAL_SECRET_ACCESS_KEY" | base64)'"}}'
    ```
    
    ### 3. Update S3 Endpoint (if using custom endpoint)
    ```bash
    # For standard AWS S3, use regional endpoint
    kubectl patch secret catalog-secrets -n terrafloww-platform -p='{"data":{"endpoint":"'$(echo -n "s3.your-region.amazonaws.com" | base64)'"}}'
    ```
    
    ### 4. Update Region
    ```bash
    kubectl patch secret catalog-secrets -n terrafloww-platform -p='{"data":{"region":"'$(echo -n "your-aws-region" | base64)'"}}'
    ```
    
    ## Alternative: Using AWS IAM Roles for Service Accounts (IRSA)
    
    For production deployments, consider using IRSA instead of static credentials:
    
    1. Create an IAM role with S3 permissions
    2. Associate the role with the Kubernetes service account
    3. Remove the access_key_id and secret_access_key from the secret
    
    ## Verification
    
    After updating secrets, verify they are correct:
    ```bash
    # Check secret values (base64 decoded)
    kubectl get secret catalog-secrets -n terrafloww-platform -o jsonpath='{.data.bucket}' | base64 -d
    kubectl get secret catalog-secrets -n terrafloww-platform -o jsonpath='{.data.region}' | base64 -d
    ```
