# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# Ray Cluster configuration for AWS EKS

apiVersion: ray.io/v1
kind: RayCluster
metadata:
  name: terrafloww-ray-cluster
  namespace: terrafloww-platform
  labels:
    app: terrafloww
    component: ray-cluster
spec:
  rayVersion: '2.8.0'
  enableInTreeAutoscaling: false
  autoscalerOptions:
    upscalingMode: Default
    idleTimeoutSeconds: 60
    imagePullPolicy: Always
    securityContext: {}
    env: []
    resources:
      limits:
        cpu: "500m"
        memory: "512Mi"
      requests:
        cpu: "500m"
        memory: "512Mi"
  headGroupSpec:
    serviceType: ClusterIP
    rayStartParams:
      dashboard-host: '0.0.0.0'
      num-cpus: '0'  # Head node doesn't participate in computation
      object-store-memory: '536870912'  # 512MB object store
      dashboard-port: '8265'
    template:
      metadata:
        labels:
          app: terrafloww
          component: ray-head
          ray.io/cluster: terrafloww-ray-cluster
          ray.io/node-type: head
      spec:
        containers:
        - name: ray-head
          image: {{RAY_IMAGE}}  # Will be replaced by deployment script
          imagePullPolicy: Always
          ports:
          - containerPort: 6379
            name: gcs-server
          - containerPort: 8265
            name: dashboard
          - containerPort: 10001
            name: client
          resources:
            limits:
              cpu: "2"
              memory: "4Gi"
            requests:
              cpu: "1"
              memory: "2Gi"
          env:
          - name: RAY_DISABLE_IMPORT_WARNING
            value: "1"
          - name: RAY_USAGE_STATS_ENABLED
            value: "0"
          - name: RAY_DEDUP_LOGS
            value: "0"
          # AWS-specific environment variables
          - name: AWS_DEFAULT_REGION
            value: "us-west-2"
          - name: AWS_REGION
            value: "us-west-2"
          volumeMounts:
          - mountPath: /tmp/ray
            name: ray-logs
        volumes:
        - name: ray-logs
          emptyDir: {}
        nodeSelector:
          kubernetes.io/arch: amd64
        tolerations:
        - key: "ray.io/node-type"
          operator: "Equal"
          value: "head"
          effect: "NoSchedule"
  workerGroupSpecs:
  - replicas: 2
    minReplicas: 1
    maxReplicas: 4
    groupName: worker-group
    rayStartParams:
      num-cpus: '2'
      object-store-memory: '1073741824'  # 1GB object store
    template:
      metadata:
        labels:
          app: terrafloww
          component: ray-worker
          ray.io/cluster: terrafloww-ray-cluster
          ray.io/node-type: worker
      spec:
        containers:
        - name: ray-worker
          image: {{RAY_IMAGE}}  # Will be replaced by deployment script
          imagePullPolicy: Always
          resources:
            limits:
              cpu: "2"
              memory: "4Gi"
            requests:
              cpu: "1"
              memory: "2Gi"
          env:
          - name: RAY_DISABLE_IMPORT_WARNING
            value: "1"
          - name: RAY_USAGE_STATS_ENABLED
            value: "0"
          - name: RAY_DEDUP_LOGS
            value: "0"
          # AWS-specific environment variables
          - name: AWS_DEFAULT_REGION
            value: "us-west-2"
          - name: AWS_REGION
            value: "us-west-2"
          # HTTP optimization environment variable
          - name: TFW_USE_HTTP_POOL
            value: "true"
          volumeMounts:
          - mountPath: /tmp/ray
            name: ray-logs
        volumes:
        - name: ray-logs
          emptyDir: {}
        nodeSelector:
          kubernetes.io/arch: amd64
        tolerations:
        - key: "ray.io/node-type"
          operator: "Equal"
          value: "worker"
          effect: "NoSchedule"
---
apiVersion: v1
kind: Service
metadata:
  name: terrafloww-ray-cluster-head-svc
  namespace: terrafloww-platform
  labels:
    app: terrafloww
    component: ray-head-service
spec:
  type: ClusterIP
  selector:
    ray.io/cluster: terrafloww-ray-cluster
    ray.io/node-type: head
  ports:
  - name: gcs-server
    port: 6379
    targetPort: 6379
  - name: dashboard
    port: 8265
    targetPort: 8265
  - name: client
    port: 10001
    targetPort: 10001
---
# Optional: Service for external access to Ray Dashboard
apiVersion: v1
kind: Service
metadata:
  name: terrafloww-ray-dashboard-external
  namespace: terrafloww-platform
  labels:
    app: terrafloww
    component: ray-dashboard-external
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-scheme: "internet-facing"
spec:
  type: LoadBalancer
  selector:
    ray.io/cluster: terrafloww-ray-cluster
    ray.io/node-type: head
  ports:
  - name: dashboard
    port: 8265
    targetPort: 8265
    protocol: TCP
