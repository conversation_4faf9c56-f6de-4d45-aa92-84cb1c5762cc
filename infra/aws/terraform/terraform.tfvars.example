# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# Example Terraform variables for Terrafloww Platform on AWS EKS
# Copy this file to terraform.tfvars and customize the values

# AWS Configuration
aws_region  = "us-west-2"
environment = "dev"

# EKS Cluster Configuration
cluster_name       = "terrafloww-eks-cluster"
kubernetes_version = "1.28"

# Network Configuration
vpc_cidr = "10.0.0.0/16"

public_subnet_cidrs = [
  "10.0.1.0/24",  # us-west-2a
  "10.0.2.0/24",  # us-west-2b
  "10.0.3.0/24"   # us-west-2c
]

private_subnet_cidrs = [
  "10.0.4.0/24",  # us-west-2a
  "10.0.5.0/24",  # us-west-2b
  "10.0.6.0/24"   # us-west-2c
]

# Security Configuration
cluster_endpoint_public_access_cidrs = [
  "0.0.0.0/0"  # Allow access from anywhere (restrict in production)
]

# Node Group Configuration
node_instance_types = ["m5.large"]  # 2 vCPU, 8 GB RAM
node_desired_size   = 3
node_max_size       = 6
node_min_size       = 1

# Feature Flags
enable_irsa                        = true
enable_cluster_autoscaler          = true
enable_aws_load_balancer_controller = true
enable_ebs_csi_driver             = true

# Additional Tags
tags = {
  Project     = "Terrafloww"
  Environment = "dev"
  Owner       = "platform-team"
  CostCenter  = "engineering"
}

# Production Configuration Example (uncomment and modify for production)
# aws_region  = "us-west-2"
# environment = "prod"
# cluster_name = "terrafloww-prod-cluster"
# 
# # Production-grade instance types
# node_instance_types = ["m5.xlarge"]  # 4 vCPU, 16 GB RAM
# node_desired_size   = 5
# node_max_size       = 10
# node_min_size       = 3
# 
# # Restrict access in production
# cluster_endpoint_public_access_cidrs = [
#   "***********/24",  # Your office IP range
#   "************/24"  # Your VPN IP range
# ]

# Development Configuration Example
# aws_region  = "us-west-2"
# environment = "dev"
# cluster_name = "terrafloww-dev-cluster"
# 
# # Cost-optimized for development
# node_instance_types = ["t3.medium"]  # 2 vCPU, 4 GB RAM
# node_desired_size   = 2
# node_max_size       = 4
# node_min_size       = 1

# Staging Configuration Example
# aws_region  = "us-west-2"
# environment = "staging"
# cluster_name = "terrafloww-staging-cluster"
# 
# # Similar to production but smaller
# node_instance_types = ["m5.large"]  # 2 vCPU, 8 GB RAM
# node_desired_size   = 3
# node_max_size       = 6
# node_min_size       = 2
