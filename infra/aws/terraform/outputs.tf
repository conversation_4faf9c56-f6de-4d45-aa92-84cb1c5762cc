# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# Terraform outputs for Terrafloww Platform on AWS EKS

output "cluster_id" {
  description = "EKS cluster ID"
  value       = aws_eks_cluster.terrafloww_cluster.id
}

output "cluster_arn" {
  description = "EKS cluster ARN"
  value       = aws_eks_cluster.terrafloww_cluster.arn
}

output "cluster_endpoint" {
  description = "Endpoint for EKS control plane"
  value       = aws_eks_cluster.terrafloww_cluster.endpoint
}

output "cluster_security_group_id" {
  description = "Security group ids attached to the cluster control plane"
  value       = aws_eks_cluster.terrafloww_cluster.vpc_config[0].cluster_security_group_id
}

output "cluster_iam_role_name" {
  description = "IAM role name associated with EKS cluster"
  value       = aws_iam_role.eks_cluster_role.name
}

output "cluster_iam_role_arn" {
  description = "IAM role ARN associated with EKS cluster"
  value       = aws_iam_role.eks_cluster_role.arn
}

output "cluster_certificate_authority_data" {
  description = "Base64 encoded certificate data required to communicate with the cluster"
  value       = aws_eks_cluster.terrafloww_cluster.certificate_authority[0].data
}

output "cluster_primary_security_group_id" {
  description = "Cluster security group that was created by Amazon EKS for the cluster"
  value       = aws_eks_cluster.terrafloww_cluster.vpc_config[0].cluster_security_group_id
}

output "node_groups" {
  description = "EKS node groups"
  value       = aws_eks_node_group.terrafloww_nodes
}

output "node_security_group_id" {
  description = "ID of the node shared security group"
  value       = aws_eks_node_group.terrafloww_nodes.remote_access[0].source_security_group_ids
}

output "vpc_id" {
  description = "ID of the VPC where the cluster and its nodes will be provisioned"
  value       = aws_vpc.terrafloww_vpc.id
}

output "vpc_cidr_block" {
  description = "The CIDR block of the VPC"
  value       = aws_vpc.terrafloww_vpc.cidr_block
}

output "private_subnets" {
  description = "List of IDs of private subnets"
  value       = aws_subnet.private_subnets[*].id
}

output "public_subnets" {
  description = "List of IDs of public subnets"
  value       = aws_subnet.public_subnets[*].id
}

output "ecr_ray_custom_repository_url" {
  description = "URL of the ECR repository for Ray custom image"
  value       = aws_ecr_repository.ray_custom.repository_url
}

output "ecr_processing_engine_repository_url" {
  description = "URL of the ECR repository for Processing Engine image"
  value       = aws_ecr_repository.processing_engine.repository_url
}

output "aws_account_id" {
  description = "AWS Account ID"
  value       = data.aws_caller_identity.current.account_id
}

output "aws_region" {
  description = "AWS Region"
  value       = var.aws_region
}

# Kubeconfig command
output "configure_kubectl" {
  description = "Configure kubectl: make sure you're able to connect to the cluster"
  value       = "aws eks --region ${var.aws_region} update-kubeconfig --name ${var.cluster_name}"
}

# ECR login command
output "ecr_login_command" {
  description = "Command to login to ECR"
  value       = "aws ecr get-login-password --region ${var.aws_region} | docker login --username AWS --password-stdin ${data.aws_caller_identity.current.account_id}.dkr.ecr.${var.aws_region}.amazonaws.com"
}

# Useful commands
output "useful_commands" {
  description = "Useful commands for managing the cluster"
  value = {
    update_kubeconfig = "aws eks --region ${var.aws_region} update-kubeconfig --name ${var.cluster_name}"
    ecr_login        = "aws ecr get-login-password --region ${var.aws_region} | docker login --username AWS --password-stdin ${data.aws_caller_identity.current.account_id}.dkr.ecr.${var.aws_region}.amazonaws.com"
    get_nodes        = "kubectl get nodes"
    get_pods         = "kubectl get pods --all-namespaces"
    cluster_info     = "kubectl cluster-info"
  }
}
