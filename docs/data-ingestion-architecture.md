# Cloud-Native Data Ingestion Platform Architecture

## Overview

This document outlines the architecture and implementation plan for a modular, event-driven data ingestion platform that can handle multiple data sources including STAC imagery, weather APIs, traffic data, and other external data sources.

## Current State vs Future Vision

**Current**: Local script execution for Sentinel-2 STAC ingestion  
**Immediate Goal**: Kubernetes-based Sentinel-2 ingestion with modular design  
**Future Vision**: Event-driven multi-source data ingestion platform  

## Architecture Principles

### 1. Plugin-Based Modularity
- **Base Framework**: Common interfaces and utilities
- **Data Source Plugins**: Specific implementations for each data type
- **Configuration-Driven**: Each plugin has its own configuration schema
- **Extensible**: Easy to add new data sources without core changes

### 2. Event-Driven Processing
- **AWS EventBridge**: Central event routing hub
- **SQS Queues**: Reliable message delivery and buffering
- **Kubernetes Jobs**: Scalable execution environment
- **Dead Letter Queues**: Error handling and retry logic

### 3. Cloud-Native Design
- **Containerized**: Docker containers for consistent deployment
- **Kubernetes-Native**: Jobs, ConfigMaps, Secrets, RBAC
- **S3 Storage**: Delta Lake tables in AWS S3
- **Observability**: Structured logging and metrics

## Directory Structure

```
internal-platform-v2/
├── services/
│   └── data-ingestion/
│       ├── framework/
│       │   ├── __init__.py
│       │   ├── base_plugin.py      # Plugin interface
│       │   ├── config.py           # Configuration management
│       │   ├── utils.py            # Common utilities
│       │   └── job_manager.py      # K8s job management
│       ├── plugins/
│       │   ├── __init__.py
│       │   ├── stac_plugin.py      # STAC/COG ingestion
│       │   ├── weather_plugin.py   # Weather API (future)
│       │   └── traffic_plugin.py   # Traffic data (future)
│       ├── consumers/
│       │   ├── __init__.py
│       │   ├── sqs_consumer.py     # SQS message consumer
│       │   └── api_server.py       # REST API (future)
│       ├── main.py                 # Plugin executor
│       ├── Dockerfile
│       └── requirements.txt
├── infra/k8s/
│   ├── data-ingestion/
│   │   ├── job-template.yaml       # Job template
│   │   ├── rbac.yaml              # Service account & RBAC
│   │   ├── configmap.yaml         # Default configurations
│   │   └── sqs-consumer.yaml      # SQS consumer deployment
└── scripts/
    ├── deploy-data-ingestion.sh
    ├── trigger-ingestion.sh
    └── update-k8s-secrets.sh
```

## Plugin Interface

```python
from abc import ABC, abstractmethod
from typing import Dict, Any, List

class BasePlugin(ABC):
    """Base class for all data ingestion plugins."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = self._setup_logging()
    
    @abstractmethod
    def validate_config(self) -> bool:
        """Validate plugin configuration."""
        pass
    
    @abstractmethod
    def ingest(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute data ingestion with given parameters."""
        pass
    
    @abstractmethod
    def get_schema(self) -> Dict[str, Any]:
        """Return parameter schema for this plugin."""
        pass
    
    def _setup_logging(self):
        """Setup structured logging for the plugin."""
        pass
```

## Event Flow Architecture

### Current Implementation (Phase 1)
```
Manual Trigger → K8s Job → STAC Plugin → Delta Lake
```

### Future Implementation (Phase 2-3)
```
SNS Notification → EventBridge → SQS Queue → K8s Consumer → K8s Job → Plugin → Delta Lake
```

## AWS Integration Design

### EventBridge Rules
- **Sentinel-2 Events**: Route to `stac-ingestion-queue`
- **Weather Events**: Route to `weather-ingestion-queue`
- **Traffic Events**: Route to `traffic-ingestion-queue`

### SQS Message Format
```json
{
  "plugin": "stac",
  "source": "sentinel-2",
  "parameters": {
    "collection": "sentinel-2-l2a",
    "bbox": [-122.5, 37.7, -122.3, 37.9],
    "datetime": "2024-06-23T10:00:00Z",
    "max_items": 10,
    "batch_size": 1
  },
  "priority": "normal",
  "retry_count": 0,
  "correlation_id": "uuid-here"
}
```

## Implementation Phases

### Phase 1: Basic Kubernetes Ingestion (Immediate)
**Jira Epic**: TF-74  
**Story**: TF-75 - Modular Data Ingestion Framework  

**Subtasks**:
- TF-77: Create base ingestion framework with plugin interface
- TF-78: Implement STAC ingestion plugin (Sentinel-2)
- TF-79: Containerize ingestion framework
- TF-80: Deploy K8s Job template for on-demand ingestion
- TF-81: Update K8s secrets with AWS credentials

**Deliverables**:
- ✅ Plugin-based framework
- ✅ STAC plugin for Sentinel-2
- ✅ Docker container
- ✅ K8s Job template
- ✅ Deployment scripts

### Phase 2: API and Monitoring (Short-term)
**Story**: TF-82 - Ingestion API and Monitoring (Future)

**Features**:
- REST API for triggering ingestion jobs
- Job status monitoring and history
- Metrics and alerting
- Authentication and authorization

### Phase 3: Event-Driven Integration (Medium-term)
**Story**: TF-76 - AWS Event-Driven Integration

**Features**:
- SQS consumer service
- EventBridge integration
- Dead letter queue handling
- Lambda integration documentation

## Configuration Management

### Plugin Configuration
Each plugin has its own configuration file:

```yaml
# plugins/stac_config.yaml
stac:
  default_api: "https://earth-search.aws.element84.com/v1"
  default_collection: "sentinel-2-l2a"
  default_batch_size: 10
  default_max_items: 100
  supported_collections:
    - sentinel-2-l2a
    - landsat-c2-l2
  delta_table_name: "ext_stac_datasets"
```

### Kubernetes Configuration
- **Secrets**: AWS credentials from catalog-secrets.yaml
- **ConfigMaps**: Plugin configurations and defaults
- **RBAC**: Minimal permissions for job creation and secret access

## Monitoring and Observability

### Logging
- **Structured JSON logs** for K8s log aggregation
- **Plugin-specific log levels** and contexts
- **Correlation IDs** for tracing requests across services

### Metrics
- Job completion rates and durations
- Data volume processed (scenes, assets, bytes)
- Error rates by plugin and data source
- Queue depth and processing lag

### Alerting
- Failed job notifications
- Long-running job alerts
- S3 connectivity issues
- Resource exhaustion warnings

## Security Considerations

### Credentials Management
- AWS credentials stored in K8s secrets
- Rotation policies for access keys
- Least privilege access to S3 buckets

### Network Security
- Network policies for pod-to-pod communication
- Egress controls for external API access
- TLS encryption for all external communications

### RBAC
- Service accounts with minimal required permissions
- Namespace isolation for different environments
- Audit logging for all K8s API operations

## Future Enhancements

### Additional Data Sources
- **Weather APIs**: OpenWeatherMap, NOAA, etc.
- **Traffic Data**: Google Traffic, HERE, TomTom
- **IoT Sensors**: Real-time sensor data streams
- **Social Media**: Twitter, Reddit APIs for event detection

### Advanced Features
- **Data Quality Validation**: Schema validation and data profiling
- **Incremental Ingestion**: Change detection and delta processing
- **Data Lineage**: Track data provenance and transformations
- **Cost Optimization**: Intelligent scheduling and resource allocation

## Getting Started

### Prerequisites
- Kubernetes cluster with RBAC enabled
- AWS credentials with S3 access
- Docker registry access
- kubectl configured for target cluster

### Quick Start
```bash
# 1. Update K8s secrets
./scripts/update-k8s-secrets.sh

# 2. Deploy ingestion framework
./scripts/deploy-data-ingestion.sh

# 3. Trigger Sentinel-2 ingestion
./scripts/trigger-ingestion.sh stac sentinel-2 \
  --bbox="-122.5,37.7,-122.3,37.9" \
  --datetime="2024-06-01/2024-06-25" \
  --max-items=10
```

---

**Last Updated**: 2024-06-23  
**Version**: 1.0  
**Status**: Architecture Design Complete, Implementation In Progress
