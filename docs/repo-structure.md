# Repository Structure

## Main Components

### SDK Layer (Moved to Separate Repository)
```markdown
terrafloww-sdk-public/
└── src/
    └── terrafloww/
        ├── __init__.py               ✔  # exports load, Workflow
        ├── workflow.py               ✔  # Workflow class (formerly GeoImageCollection)
        ├── data.py                   ✔  # load() function (formerly loaders.py)
        └── processing_engine/        ✔  # gRPC protobuf definitions
```

### Test Suite (In Public SDK Repository)
```markdown
terrafloww-sdk-public/
└── tests/
    ├── test_basic.py                 ✔  # Basic SDK tests
    └── test_public_sdk.py            ✔  # End-to-end integration test
```

### Services
```markdown
services/
└── processing_engine/
    └── app/
        ├── grpc_service.py           ✔  # receives ExecuteWorkflow, kicks off RayDriver
        ├── flight_server.py          ✔  # streams results back via Arrow-Flight
        └── __init__.py               –
```

### Core Engine
```markdown
libs/
└── tfw_engine_core/
    └── src/
        └── terrafloww/
            └── engine_core/
                ├── runtime_ray/
                │   ├── driver.py      ✔  # orchestrates Ray tasks + writes Flight cache
                │   ├── planner.py     ✔  # turns high-level plan into WindowSpecs
                │   └── worker.py      ✔  # fetches COGs, runs kernels, returns RecordBatches
                │
                ├── process.py        ✔  # contains the `_ndvi_kernel` implementation
                ├── catalog_client.py ✔  # STAC catalog lookup + band-alias resolution
                ├── registry.py       –  # generic function registry
                ├── utils.py          –  # reprojection, Arrow/NumPy helpers
                └── spectral/
                    └── ndvi.py       ✔  # NDVI wrapper (if split out)
                └── bands/
                    └── sentinel2.py  –  # per-collection mapping
```

## NDVI Flow Components

### SDK Layer (Separate Repository)
- `workflow.py`: Builds the DAG, calls gRPC & Flight (Workflow class)
- `data.py`: Lazy-loads Sentinel-2 COG URLs (load function)

### Test (In Public SDK Repository)
- `test_public_sdk.py`: NDVI smoke test

### gRPC API
- `grpc_service.py`: Accepts ExecuteWorkflow

### Ray Driver Stack
- `driver.py`: Drives Ray jobs, collects + caches results
- `planner.py`: Slices AOI into tiles (WindowSpecs)
- `worker.py`: Fetches imagery & applies _ndvi_kernel

### Processing
- `process.py`: Core NDVI logic
- `ndvi.py`: Kernel entry-point (if separated)

### Catalog
- `catalog_client.py`: Resolves band aliases, queries STAC

### Flight Server
- `flight_server.py`: Serves Arrow table on do_get

## Supporting Files

### SDK
- `exceptions.py`: Custom error handling

### Flight Server
- Additional endpoints: `do_put`, `list_flights`, `get_flight_info`

### Core Engine Support
- `registry.py`: Function registry
- `utils.py`: General reprojection and Arrow helpers
- `bands/sentinel2.py`: Collection mapping

### Documentation & Infrastructure
- `README.md`
- CI configurations
- Dockerfiles
- Examples