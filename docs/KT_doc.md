# tf-data-backend Architecture Documentation

*Last Updated: 2025-01-08*

## High-Level Philosophy

### Lazy, Declarative Pipelines

The SDK lets you build a description of what you want (e.g., "load Sentinel-2, compute NDVI, take first 2 tiles") without pulling data immediately.

This mirrors patterns in Spark or Dask: you declare transformations, then call `.compute()`.

### Separation of Concerns

- **SDK**: User-facing, constructs pipelines and handles retries/streaming
- **gRPC API**: Receives workflow plans, kicks off execution
- **Ray Driver**: Distributes the work, fetches imagery, runs kernels, collates results
- **Flight Server**: Streams results back to client as Arrow tables

### Columnar, Tile-Based Compute

- Imagery is chopped into small tiles (WindowSpecs)
- Each tile is processed in parallel, producing a PyArrow RecordBatch
- Batches get concatenated into a single `pa.Table` for easy downstream analysis

### In-Memory Caching & Idempotency

- Once a workflow execution_id finishes, its results live in Flight server cache
- Re-requests with the same execution_id hit the cache (no re-compute)

## Current Performance Characteristics

### Task Granularity
- **Current**: One Ray task per spatial window (scene_id, tile_r, tile_c)
- **Issue**: Fine-grained tasks create Ray scheduling overhead
- **Impact**: High task creation/serialization overhead for large workflows

### HTTP Connection Management
- **Current**: HTTPActorPool with persistent aiohttp sessions (feature-flagged)
- **Fallback**: New httpx client per worker task
- **Optimization**: Connection reuse and range merging implemented

## Current Architecture Overview

```mermaid
flowchart TB
    subgraph "SDK Layer"
      A[collections.py] --> B[loaders.py]
      A --> C[exceptions.py]
    end

    subgraph "Processing Engine"
      D[grpc_service.py] --> E[driver.RayDriver]
      E --> F[planner.py]
      F --> G[worker.py]
      G --> H[process.py & kernels]
      E --> I[catalog_client.py]
      E --> J[HTTPActorPool]
    end

    subgraph "Flight Server"
      K[flight_server.py] --> L[result caches]
    end

    subgraph "Ray Cluster"
      M[Ray Head] --> N[Ray Workers]
      J --> N
      G --> N
    end

    A -->|gRPC ExecuteWorkflow| D
    A -->|Flight do_get| K
    E -->|WindowSpecs| G
    G -->|RecordBatch| K
    F -->|STAC Query| I
```

## Data Flow Architecture

### 1. Plan → WindowSpec Generation
- **planner.py** queries STAC catalog via **catalog_client.py**
- Groups assets by scene and generates **WindowSpec** objects
- Each WindowSpec contains: COG URL, byte range, tile coordinates, processing metadata
- Implements temporal diversity prioritization (interleaves scenes)

### 2. WindowSpec → Ray Task Submission
- **driver.py** groups WindowSpecs by spatial window (scene_id, tile_r, tile_c)
- Submits one Ray task per spatial window to **process_batch_on_worker**
- Passes HTTPActorPool handles for persistent connection reuse

### 3. Worker Data Processing Pipeline
- **worker.py** fetches raw COG data via HTTPActorPool or httpx fallback
- Decodes TIFF tiles, applies scale/offset, stacks bands
- Applies kernels (e.g., NDVI) from **process.py** registry
- Packages results into Arrow RecordBatch format

### 4. Flight Server Upload
- Workers upload RecordBatch directly to **flight_server.py**
- Driver signals completion when all workers finish
- Results cached in Flight server for subsequent requests
# SDK Layer

## collections.py

- Core class: `GeoImageCollection`
- Methods like `.apply_func()`, `.head()` build an internal DAG of operations
- `.compute()`:
  - Calls gRPC API to start workflow (ExecuteWorkflow)
  - Polls Flight server via `do_get(ticket)` (with retry/backoff) until results arrive
  - Wraps results into a `pa.Table`

## loaders.py

- Helpers for defining lazy data sources (e.g., STAC-based Sentinel-2)
- Encapsulates how to fetch bands, metadata, AOI defaults, etc.

## exceptions.py

- Custom error types (e.g., timeouts, parsing errors) surfaced to the user

# gRPC API

## grpc_service.py

### Main Functionality
- Entry point for all pipeline executions
- Exposes `ExecuteWorkflow(WorkflowPlan) → ExecuteWorkflowResponse`

### Workflow Process
1. Validates incoming plan (collection name, function IDs, filters, head limits)
2. Instantiates singleton `RayDriver`
3. Calls `start_execution(execution_id, plan)`
4. Immediately returns `execution_id + flight_ticket` for client polling

# Ray Driver & Planner

## driver.py (RayDriver)

### Current Implementation

**Workflow Lifecycle Management:**
1. Initialize Ray cluster connection (singleton pattern)
2. Initialize HTTPActorPool for persistent connections (if enabled)
3. Consume async generator of WindowSpecs from planner
4. Group WindowSpecs by spatial window (scene_id, tile_r, tile_c)
5. Submit one Ray task per spatial window to `process_batch_on_worker`
6. Workers upload results directly to Flight server
7. Signal completion to Flight server when all tasks finish

**Key Features:**
- **Spatial Window Grouping**: Groups multiple bands for same spatial location
- **Band Requirement Validation**: Ensures all required bands available before processing
- **HTTPActorPool Integration**: Passes actor handles to workers for connection reuse
- **Direct Flight Upload**: Workers stream results directly, no driver aggregation

**Current Bottlenecks:**
- **Fine-grained tasks**: One Ray task per spatial window creates scheduling overhead
- **No batching**: Each spatial window processed individually
- **Limited parallelism**: Task granularity doesn't match cluster resources optimally

## planner.py

### Current Implementation

**Workflow Planning Process:**
1. Resolves band aliases via CatalogClient (supports multiple band naming schemes)
2. Queries STAC catalog for available assets with spatial/temporal filtering
3. Groups assets by scene and computes intersecting tiles/windows
4. Implements temporal diversity prioritization (interleaves scenes)
5. Yields WindowSpec objects with complete processing metadata

**Key Features:**
- **Band Alias Resolution**: Maps operation bands (red, nir) to catalog bands (B04, B08)
- **Spatial Intersection**: Uses grid.py to compute tile/window intersections with AOI
- **Temporal Diversity**: Interleaves windows across scenes for better parallelism
- **Complete Metadata**: WindowSpec contains all info needed for processing

**Current Limitations:**
- **Random Spatial Order**: Within scenes, tiles processed in arbitrary order
- **No Spatial Locality**: Poor S3 caching due to random access patterns
- **Limited Optimization**: No consideration of data locality or prefetching

## worker.py

### Ray Process Execution

1. Fetches COGs for each band via httpx (partial-range GET)
2. Decodes with rasterio into NumPy arrays
3. Stacks into an Arrow RecordBatch
4. Calls kernel function (e.g., _ndvi_kernel) to compute new arrays
5. Returns RecordBatch with original columns + new outputs

# Processing & Kernels

## process.py

### NDVI Kernel Implementation

```python
def _ndvi_kernel(batch: RecordBatch, red_band: str, nir_band: str) -> RecordBatch:
    # extract arrays, compute (nir - red)/(nir + red), handle divide-by-zero
    # append as new 'ndvi' column
```

## bands/sentinel2.py

- Collection-specific defaults:
  - Maps "red" → "B04"
  - Maps "nir" → "B08"

# Arrow-Flight Server

## Overview

- Runs alongside gRPC server (default port 50052)
- Exposes synchronous and asynchronous methods

## Synchronous Methods

### do_get(ticket)

1. Parses execution_id
2. Creates placeholder status if missing
3. On completion:
   - Retrieves pa.Table from cache
   - Returns RecordBatchStream
4. On failure: Throws error
5. Otherwise: Throws FlightUnavailableError (triggers SDK retry)

## Caching & Locks

```python
LOCAL_FLIGHT_RESULT_CACHE: Dict[str, pa.Table]
LOCAL_FLIGHT_STATUS_CACHE: Dict[str, {"status": ..., "details": ...}]
_FLIGHT_LOCK: Protects async writes
FlightServer._sync_lock: Protects sync reads/writes
```

# End-to-End Flow

## User Code

```python
collection = GeoImageCollection("sentinel-2-l2a")\
               .apply_func("terrafloww.spectral.ndvi")\
               .head(2)
result = collection.compute()
```

## Performance Analysis & Optimization Opportunities

### Current Performance Bottlenecks

#### 1. Ray Scheduling Overhead
- **Issue**: One Ray task per spatial window creates fine-grained parallelism
- **Impact**: High task creation, serialization, and scheduling overhead
- **Measurement**: Ray timeline shows significant `task:deserialize_arguments` time
- **Solution**: Implement chunky task granularity (batch multiple windows per task)

#### 2. HTTP Connection Overhead
- **Current**: HTTPActorPool with persistent aiohttp sessions (feature-flagged)
- **Fallback**: New httpx client per worker task
- **Impact**: TCP handshake overhead for each new connection
- **Optimization**: Connection reuse reduces 123s → 30-60s (2-4x improvement)

#### 3. S3 Access Pattern Inefficiency
- **Issue**: Random spatial order within scenes leads to poor S3 caching
- **Impact**: Inefficient range requests, poor CDN/cache utilization
- **Solution**: Hilbert curve sorting for spatial locality optimization

#### 4. CPU-bound Processing Limitations
- **Current**: CPU-only processing with no GPU acceleration
- **Opportunity**: GPU-direct pipeline for inference workloads
- **Potential**: Zero-copy data transfer, persistent GPU actors

### Optimization Roadmap

#### Phase 1: Python-level Optimizations
1. **Chunky Task Granularity**: Batch multiple spatial windows per Ray task
2. **HTTPActorPool Default**: Make persistent connections the default
3. **Hilbert Curve Sorting**: Implement spatial locality in planner
4. **Task Pipelining**: Use ray.wait for backpressure management

#### Phase 2: GPU-Direct Pipeline
1. **Long-lived GPU Actors**: Persistent InferenceWorker actors with VRAM allocation
2. **CPU-to-GPU Handoff**: Zero-copy Arrow→NumPy→Torch pipeline
3. **Ray GPU Objects**: Leverage Ray 2.48.0 GPU object support
4. **Model Management**: Hot-loading, versioning, CPU fallback

## Flow Breakdown

### SDK Layer

- Serializes plan proto
- Makes gRPC call to grpc_service.py
- Starts polling Flight with ticket

### gRPC Service

- Calls RayDriver to start execution

### Ray Execution

#### Planner

- Yields two WindowSpecs

#### Driver

- Fires off two Ray tasks

#### Workers

- Fetch imagery
- Compute NDVI
- Return two RecordBatches

#### Driver

- Concatenates into single pa.Table with two rows

Writes into in-memory cache and marks “COMPLETED.”

### Flight Server

- Each SDK poll hits do_get → sees PENDING until the driver finishes
- Once “COMPLETED,” returns the pa.Table as a RecordBatchStream

Once “COMPLETED,” returns the pa.Table as a RecordBatchStream.

### SDK Layer

- Reads the stream
- Converts to pa.Table
- Returns the final result

### User

Now has a table with two rows and columns:

```text
['chunk_id','raster_data','shape','bounds','crs','datetime','bands','label','quality','ndvi']
```

# Codebase Navigation

1. **SDK Entry Point (Separate Repository)**
   - Start with: `terrafloww-sdk-public/src/terrafloww/workflow.py`
   - This is your main entrypoint (Workflow class)

2. **gRPC Flow**
   - Client stub calls: `services/processing_engine/app/grpc_service.py`

3. **Execution Flow**
   - `runtime_ray/driver.py` → `planner.py` → `worker.py`

4. **Kernel Location**
   - Kernels: `process.py`
   - Band mappers: `bands/` directory

5. **Flight Server**
   - Main server: `services/processing_engine/app/flight_server.py`
   - Caches: Two global dicts in `driver.py`
