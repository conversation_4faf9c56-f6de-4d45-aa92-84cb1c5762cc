# Schema and Utility Organization

This document describes the organization of schemas and utilities in the Terrafloww Platform repository.

## Core Principles

The repository follows these core principles for code organization:

1. **Single Source of Truth**: Each schema or utility has one canonical location
2. **Clear Boundaries**: Libraries have well-defined responsibilities
3. **Service Encapsulation**: Service-specific logic stays within the service
4. **Minimal Dependencies**: Libraries depend only on what they need

## Schema Organization

### tfw_raster_schemas

The `tfw_raster_schemas` library is the canonical source for all Arrow schemas used across the platform:

- `raster.py`: Core raster data schemas (e.g., `RASTER_CHUNK_SCHEMA`)
- `stac.py`: STAC-related schemas (e.g., `EXT_STAC_IMG_DATASETS_SCHEMA`)
- `datasets.py`: Dataset metadata schemas (e.g., `IMG_DATASETS_SCHEMA`)
- `grid.py`: Grid template schemas (e.g., `GRID_TEMPLATES_SCHEMA`)

All services and libraries that need these schemas should import them directly from `tfw_raster_schemas`.

Example:
```python
from tfw_raster_schemas import RASTER_CHUNK_SCHEMA, IMG_DATASETS_SCHEMA
```

### Service-Specific Schemas

Schemas that are specific to a particular service (e.g., `JOBS_SCHEMA` for the Metadata Service) should be defined within that service's codebase:

```
services/metadata_service/app/models/schemas.py
```

## Utility Organization

### tfw_core_utils

The `tfw_core_utils` library contains generic utilities used across the platform:

- `hash.py`: Hashing utilities (e.g., `create_canonical_hash`)

Example:
```python
from tfw_core_utils import create_canonical_hash
```

### Service-Specific Utilities

Utilities that are specific to a particular service should be defined within that service's codebase:

- `services/metadata_service/app/crud/delta_utils.py`: Delta Lake table management
- `services/metadata_service/app/crud/serialization.py`: JSON serialization for Delta tables

## Delta Table Management

The Metadata Service owns all Delta table management logic:

- Table paths are defined in `services/metadata_service/app/crud/delta_utils.py`
- Table schemas are imported from `tfw_raster_schemas` or defined locally
- Table CRUD operations are implemented in service-specific modules

## API Models vs. Data Models

- **Arrow Schemas** (in `tfw_raster_schemas`): Define the structure of data stored and processed
- **Pydantic Models** (in service API modules): Define the structure of API request/response bodies

## Migration Notes

This organization was implemented as part of a cleanup effort to ensure a single source of truth for schemas and utilities. Previously, some schemas were duplicated across the codebase, and utilities were spread across multiple libraries.
