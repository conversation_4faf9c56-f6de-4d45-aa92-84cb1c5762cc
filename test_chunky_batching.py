#!/usr/bin/env python3
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Test script for chunky batching implementation.

This script tests the new chunky task granularity feature that batches
multiple spatial windows per Ray task to reduce scheduling overhead.
"""

import os
import sys
import logging
import asyncio
from unittest.mock import Mock, patch

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_window_spec(scene_id: str, tile_r: int, tile_c: int, band: str):
    """Create a test WindowSpec for testing."""
    return {
        'scene_id': scene_id,
        'tile_r': tile_r,
        'tile_c': tile_c,
        'band': band
    }

def test_batch_size_calculation():
    """Test the batch size calculation logic."""
    logger.info("Testing batch size calculation...")
    
    # Test cases: (total_windows, available_cpus, expected_batch_size)
    test_cases = [
        (4, 4, 1),    # Equal windows and CPUs
        (2, 4, 1),    # Fewer windows than CPUs
        (8, 4, 2),    # More windows than CPUs
        (20, 4, 5),   # Many windows, few CPUs
        (50, 4, 10),  # Many windows, should cap at 10
    ]
    
    for total_windows, available_cpus, expected_batch_size in test_cases:
        if total_windows <= available_cpus:
            batch_size = 1
        else:
            batch_size = max(1, min(10, (total_windows + available_cpus - 1) // available_cpus))
        
        assert batch_size == expected_batch_size, f"Failed for {total_windows} windows, {available_cpus} CPUs: got {batch_size}, expected {expected_batch_size}"
        logger.info(f"✓ {total_windows} windows, {available_cpus} CPUs → batch_size={batch_size}")
    
    logger.info("Batch size calculation tests passed!")

def test_window_batching():
    """Test the window batching logic."""
    logger.info("Testing window batching logic...")
    
    # Create test windows
    windows = [
        ("scene1", 0, 0),
        ("scene1", 0, 1), 
        ("scene2", 0, 0),
        ("scene2", 0, 1),
        ("scene3", 0, 0),
    ]
    
    # Test batching with different batch sizes
    batch_sizes = [1, 2, 3]
    
    for batch_size in batch_sizes:
        batched_windows = []
        for i in range(0, len(windows), batch_size):
            batch = windows[i:i + batch_size]
            batched_windows.append(batch)
        
        # Verify all windows are included
        flattened = [w for batch in batched_windows for w in batch]
        assert flattened == windows, f"Windows lost during batching with batch_size={batch_size}"
        
        # Verify batch sizes
        for i, batch in enumerate(batched_windows):
            if i < len(batched_windows) - 1:
                assert len(batch) == batch_size, f"Batch {i} has wrong size: {len(batch)} != {batch_size}"
            else:
                # Last batch can be smaller
                assert len(batch) <= batch_size, f"Last batch {i} too large: {len(batch)} > {batch_size}"
        
        logger.info(f"✓ Batching with batch_size={batch_size}: {len(windows)} windows → {len(batched_windows)} batches")
    
    logger.info("Window batching tests passed!")

async def test_chunky_batching_integration():
    """Test the chunky batching integration logic."""
    logger.info("Testing chunky batching integration...")

    # Test the batch calculation logic
    total_windows = 8  # 2 scenes * 2x2 tiles = 8 spatial windows
    available_cpus = 4
    expected_batch_size = 2  # 8 windows / 4 CPUs = 2

    batch_size = max(1, min(10, (total_windows + available_cpus - 1) // available_cpus))
    assert batch_size == expected_batch_size, f"Expected batch_size={expected_batch_size}, got {batch_size}"

    expected_batches = (total_windows + batch_size - 1) // batch_size  # Ceiling division
    assert expected_batches == 4, f"Expected 4 batches, calculated {expected_batches}"

    logger.info(f"✓ Integration test: {total_windows} windows → {expected_batches} batches with batch_size={batch_size}")

    logger.info("Chunky batching integration test passed!")

def main():
    """Run all chunky batching tests."""
    logger.info("Starting chunky batching tests...")
    
    try:
        # Run synchronous tests
        test_batch_size_calculation()
        test_window_batching()
        
        # Run async test
        asyncio.run(test_chunky_batching_integration())
        
        logger.info("🎉 All chunky batching tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
