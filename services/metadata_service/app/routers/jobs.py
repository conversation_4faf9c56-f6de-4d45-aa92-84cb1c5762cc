# services/metadata_service/app/routers/jobs.py
from fastapi import APIRouter, HTTPException, Body

# Import Pydantic models and CRUD functions
from terrafloww.api.models import (
    RegisterJobPayload,
    JobInfoResponse,
    JobStatusUpdatePayload,
    JobRegistrationResponse,
)
from terrafloww.api.models import jobs as jobs_crud

router = APIRouter(prefix="/jobs", tags=["Jobs"])


@router.post("/", response_model=JobRegistrationResponse, status_code=201)
async def register_new_job(payload: RegisterJobPayload = Body(...)):
    """Registers a new job to be executed."""
    artifact_versions_dict = (
        payload.input_artifact_versions.root
        if payload.input_artifact_versions
        else None
    )

    try:
        job_id = await jobs_crud.register_job(
            job_type=payload.job_type,
            # CORRECT access using the field name from the Pydantic model:
            input_artifact_versions=artifact_versions_dict,  # Pass the dict here
            parameters=payload.parameters,
        )
        response_object = JobRegistrationResponse(job_id=job_id)
        return response_object
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        print(f"API Error registering job: {e}")
        raise HTTPException(status_code=500, detail="Internal server error.")


@router.put(
    "/{job_id}", status_code=204
)  # No content response typically for PUT updates
async def update_job_status(job_id: str, payload: JobStatusUpdatePayload = Body(...)):
    """Updates the status of a job (e.g., running, completed, failed)."""
    try:
        success = await jobs_crud.update_job(  # Assuming update_job handles logic
            job_id=job_id,
            status=payload.status,
            output_dataset_id=payload.output_dataset_id,
            error_message=payload.error_message,
        )
        if not success:
            # This might indicate the job wasn't found or update failed logic in crud
            raise HTTPException(
                status_code=404, detail=f"Job {job_id} not found or update failed."
            )
        # No return body on success (204)
    except ValueError as e:  # Catch potential validation errors if crud raises them
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        print(f"API Error updating job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error.")


@router.get(
    "/{job_id}",
    response_model=JobInfoResponse,
    responses={404: {"description": "Job not found"}},
)
async def get_job_status_info(job_id: str):
    """Gets the current status and information for a specific job."""
    info = await jobs_crud.get_job_info(job_id)
    if info is None:
        raise HTTPException(status_code=404, detail=f"Job {job_id} not found.")
    return info  # FastAPI will validate against JobInfoResponse
