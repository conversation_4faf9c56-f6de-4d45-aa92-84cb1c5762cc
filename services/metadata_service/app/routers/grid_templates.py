# services/metadata_service/app/routers/grid_templates.py
from fastapi import (
    APIRouter,
    HTTPException,
    Body,
    Path,
)  # Added Depends for potential future auth

from terrafloww.libs.platform_core.models import (
    GridTemplateInfo,
    GridTemplateCreatePayload,
)  # Import models
from terrafloww.api.models import (
    grid_templates as crud,
)  # Import crud functions

# This router might be primarily for internal use or admin initially
router = APIRouter(prefix="/grid-templates", tags=["Grid Templates"])


# Endpoint to get a template by ID (useful for Ray jobs/SDK internals)
@router.get(
    "/{grid_id}",
    response_model=GridTemplateInfo,
    responses={404: {"description": "Grid Template not found"}},
)
async def read_grid_template(grid_id: str):
    """Retrieves a grid template definition by its unique ID."""
    template = await crud.get_grid_template(grid_id)
    if template is None:
        raise HTTPException(
            status_code=404, detail=f"Grid Template '{grid_id}' not found."
        )
    # Pydantic will validate the dict against GridTemplateInfo
    try:
        return template
    except Exception as e:  # Catch Pydantic validation errors if dict is bad
        print(f"Error validating grid template response for {grid_id}: {e}")
        raise HTTPException(
            status_code=500, detail="Internal server error formatting grid template."
        )


# Endpoint for ingester to potentially add templates (maybe protected later)
@router.post("/{grid_id}", response_model=GridTemplateInfo, status_code=201)
async def create_grid_template(
    payload: GridTemplateCreatePayload = Body(...),
    grid_id: str = Path(
        ...,
        description="The pre-generated unique ID for this grid template",
        examples=["SENTINEL_2_L2A_EPSG32629_10m_10980x10980"],
    ),
):
    """
    Creates a new grid template definition.
    Expects the caller (e.g., ingester) to determine the grid_id.
    If a template with the same effective definition already exists,
    it might return the existing one (CRUD logic TBD).
    """
    # Generate the grid_id based on payload properties (Needs robust logic)
    # This logic might be better placed *within* the ingester process
    template_dict_from_payload = payload.model_dump()
    template_dict_for_crud = {"grid_id": grid_id}  # Start with the string grid_id
    template_dict_for_crud.update(template_dict_from_payload)  # Add payload data

    try:
        # Use add_grid_template which handles check/add logic
        added_or_existing_id = await crud.add_grid_template(template_dict_for_crud)
        # Fetch the newly added or existing template to return full info
        final_template = await crud.get_grid_template(added_or_existing_id)

        if not final_template:
            raise HTTPException(
                status_code=500, detail="Failed to retrieve template after add."
            )

        return final_template  # Pydantic validation occurs here

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        print(f"API Error creating grid template: {e}")
        raise HTTPException(
            status_code=500, detail="Internal server error creating grid template."
        )
