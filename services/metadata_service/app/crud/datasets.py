# services/metadata_service/app/crud/datasets.py
import pyarrow as pa
import pyarrow.dataset as ds
from deltalake import write_deltalake
from deltalake.exceptions import DeltaError  # Import DeltaError
import uuid
import json
from datetime import datetime, timezone
from typing import Any, Optional, Dict

# Import schemas and utils
from tfw_raster_schemas import IMG_DATASETS_SCHEMA
from .delta_utils import (
    DATASETS_TABLE_PATH,
    get_delta_table,
    ensure_table_exists,
)
from tfw_core_utils import create_canonical_hash

# Keep DatasetDefinitionQuery for now - we'll need to create a proper model for it later
class DatasetDefinitionQuery:
    """Temporary placeholder for DatasetDefinitionQuery until properly migrated"""
    pass


# Ensure the table exists on module load (or handle in app startup)
ensure_table_exists(
    DATASETS_TABLE_PATH, IMG_DATASETS_SCHEMA, partition_cols=["name", "dataset_type"]
)


async def commit_dataset_version(
    name: str,
    dataset_type: str,
    source_job_id: str | None,
    source_artifact_versions: dict[str, int] | None,
    # --- Derived fields ---
    storage_paths: list[str] | None,
    arrow_schema: bytes | None,
    # --- Raw fields (MS+GT) ---
    cog_tile_offsets: dict[str, list[int]] | None,
    cog_tile_byte_counts: dict[str, list[int]] | None,
    cog_grid_ids: dict[str, str] | None,
    cog_dtype: dict[str, str] | None,
    cog_predictor: dict[str, int] | None,
    # --- Common fields ---
    parameters: dict[str, Any] | None,  # Allow Any type here
    quality_metrics: dict[str, float] | None,
    raw_properties: dict[str, Any] | None,  # Allow Any type here
    raw_geometry: str | None,
    status: str = "available",
    # --- Scale/Offset (Maps) ---
    cog_scale: Optional[Dict[str, float]] = None,
    cog_offset: Optional[Dict[str, float]] = None,
    # --- Raw fields (MS+GT) ---
    raw_cog_urls: Optional[Dict[str, str]] = None,

) -> dict[str, Any]:
    """Appends a new version entry for a dataset."""
    dataset_id = str(uuid.uuid4())  # Unique ID for this version instance
    commit_time = datetime.now(timezone.utc)

    # --- Calculate Hashes ---
    source_hash = create_canonical_hash(source_artifact_versions)
    params_hash = create_canonical_hash(parameters)
    # ---
    # --- Serialize complex fields to JSON strings ---
    params_str = json.dumps(parameters) if parameters else None
    raw_props_str = json.dumps(raw_properties) if raw_properties else None

    # Prepare data dictionary matching the schema
    data = {
        "dataset_id": [dataset_id],
        "name": [name],
        "dataset_type": [dataset_type],
        "timestamp": [commit_time],
        "source_job_id": [source_job_id],
        "source_artifact_versions": [source_artifact_versions or {}],
        # --- Handle Nullable Fields ---
        "storage_paths": [storage_paths or []],  # Use empty list if None
        "arrow_schema": [arrow_schema],  # None is handled by Arrow
        "raw_cog_urls": [raw_cog_urls or {}],
        "cog_tile_offsets": [cog_tile_offsets or {}],
        "cog_tile_byte_counts": [cog_tile_byte_counts or {}],
        "cog_grid_ids": [cog_grid_ids or {}],
        "cog_dtype": [cog_dtype or {}],  # ADDED FIELD
        "cog_predictor": [cog_predictor or {}],  # ADDED FIELD
        # --- Scale/Offset (Maps) ---
        "cog_scale": [cog_scale or {}],
        "cog_offset": [cog_offset or {}],

        # ---
        # --- Add Hashes ---
        "source_sig_hash": [source_hash],
        "params_sig_hash": [params_hash],
        # ---
        "parameters_json": [params_str],  # Store as JSON string
        "quality_metrics": [quality_metrics or {}],
        "raw_properties_json": [raw_props_str],  # Store as JSON string
        "raw_geometry": [raw_geometry],
        "status": [status],
    }

    try:
        table = pa.Table.from_pydict(data, schema=IMG_DATASETS_SCHEMA)
    except (pa.ArrowInvalid, TypeError) as e:  # Catch TypeError too
        print(f"Schema mismatch creating table for {name}: {e}")
        print(f"Data provided: {data}")
        raise ValueError(f"Schema mismatch creating dataset table: {e}")

    storage_options = None  # Adjust for cloud storage
    write_deltalake(
        DATASETS_TABLE_PATH,
        table,
        mode="append",
        engine="rust",
        schema_mode="merge",  # Be careful with merge if schemas might diverge
        partition_by=["name", "dataset_type"],
        storage_options=storage_options,
        name=f"Commit dataset {name} v{dataset_type}",
        description=f"Added dataset {name} type {dataset_type}",
    )

    # Get the version number of the commit we just made
    dt = get_delta_table(DATASETS_TABLE_PATH)
    version = -1  # Default value
    if dt:
        try:
            # Fetch history safely
            history = dt.history(limit=1)
            if history:
                version = history[0]["version"]
        except DeltaError as e:
            print(f"Error fetching Delta history after write: {e}")
            # Fallback or raise error? For now, log and continue with version=-1

    # Return a dict structure matching DatasetInfoResponse fields
    # Convert maps back if needed (though often handled by FastAPI/Pydantic)
    return {
        "dataset_id": dataset_id,
        "name": name,
        "dataset_type": dataset_type,
        "version": version,
        "timestamp": commit_time,
        "source_job_id": source_job_id,
        "source_artifact_versions": source_artifact_versions or {},
        "source_sig_hash": source_hash,  # Include calculated hash
        "params_sig_hash": params_hash,  # Include calculated hash
        "storage_paths": storage_paths or [],
        "arrow_schema": arrow_schema.hex() if arrow_schema else None,
        "raw_cog_urls": raw_cog_urls or {},
        "cog_tile_offsets": cog_tile_offsets or {},
        "cog_tile_byte_counts": cog_tile_byte_counts or {},
        "cog_grid_ids": cog_grid_ids or {},
        "cog_dtype": cog_dtype or {},  
        "cog_predictor": cog_predictor or {},
        "cog_scale": cog_scale or {},
        "cog_offset": cog_offset or {},
        "quality_metrics": quality_metrics or {},
        "status": status,
        "raw_properties": raw_properties or {},
        "raw_geometry": raw_geometry,
    }


async def get_dataset_info(
    name: str, version: int | str = "latest"
) -> dict[str, Any] | None:
    """Gets metadata for a specific dataset name and version. (Handles new fields)"""
    load_version = None
    if isinstance(version, int):
        load_version = version
    elif version != "latest":
        raise ValueError("Version must be an integer or 'latest'")

    dt = get_delta_table(DATASETS_TABLE_PATH, version=load_version)
    if not dt:
        return None

    try:
        # Filter by name only initially
        table = dt.to_pyarrow_table(filters=(ds.field("name") == name))

        if len(table) == 0:
            print(f"No dataset found for name '{name}'")
            return None

        # If latest was requested, find the row with the highest version (or timestamp)
        # Note: Delta versioning handles 'latest' load, but filtering *within* that version
        # requires sorting if multiple matches exist for the name (shouldn't happen with unique IDs).
        # We assume the version loaded by DeltaTable(version=...) is correct.
        # If multiple rows somehow exist for the name within that version, we take the first.
        # This logic might need refinement depending on exact partitioning/uniqueness.

        if len(table) > 0:
            # Convert the first row (most relevant for the loaded version) to dict
            result_dict_raw = table.to_pylist()[0]
            result_dict_formatted = {}

            # Revised loop logic in get_dataset_info
            for field in IMG_DATASETS_SCHEMA:
                key = field.name
                if key not in result_dict_raw:
                    result_dict_formatted[key] = None
                    continue
                value = result_dict_raw[key]

                # Handle specific types (JSON, Binary, Timestamp)
                if key == "parameters_json" and isinstance(value, str):
                    try: result_dict_formatted["parameters"] = json.loads(value)
                    except json.JSONDecodeError: result_dict_formatted["parameters"] = {}
                elif key == "raw_properties_json" and isinstance(value, str):
                    try: result_dict_formatted["raw_properties"] = json.loads(value)
                    except json.JSONDecodeError: result_dict_formatted["raw_properties"] = {}
                elif key == "arrow_schema" and isinstance(value, bytes):
                    result_dict_formatted[key] = value.hex()
                elif isinstance(value, (datetime, pa.TimestampScalar)):
                    py_dt = value.as_py() if isinstance(value, pa.TimestampScalar) else value
                    if py_dt.tzinfo is None: py_dt = py_dt.replace(tzinfo=timezone.utc)
                    else: py_dt = py_dt.astimezone(timezone.utc)
                    result_dict_formatted[key] = py_dt
                # Handle Arrow MapType -> Python dict
                elif isinstance(field.type, pa.MapType):
                    # pa.Table.to_pylist converts maps to list of tuples
                    if isinstance(value, list) and all(isinstance(item, tuple) and len(item) == 2 for item in value):
                        result_dict_formatted[key] = dict(value)
                    elif isinstance(value, list) and not value: result_dict_formatted[key] = {} # Handle empty map
                    elif isinstance(value, dict): result_dict_formatted[key] = value # Already dict (less common)
                    else:
                        print(f"Warning: Unexpected format for map field '{key}': {type(value)}, using raw value.")
                        result_dict_formatted[key] = value # Keep raw value if conversion unclear
                else:
                    result_dict_formatted[key] = value # Assign other types directly

            result_dict_formatted["version"] = dt.version()
            # Remove internal _json fields before returning
            result_dict_formatted.pop("parameters_json", None)
            result_dict_formatted.pop("raw_properties_json", None)
            return result_dict_formatted

    except Exception as e:
        print(f"Error querying or processing dataset '{name}' version '{version}': {e}")
        import traceback

        traceback.print_exc()
        return None


# --- NEW: Function to query dataset by definition ---
async def find_dataset_by_definition(
    query: DatasetDefinitionQuery,
) -> Optional[Dict[str, Any]]:
    """
    Attempts to find the latest 'available' dataset version matching the definition
    by querying pre-calculated signature hashes for sources and parameters.

    Args:
        query: A DatasetDefinitionQuery object containing source versions
               and parameters, which automatically calculates hashes.

    Returns:
        A dictionary containing {"exists": True, "dataset_id": ..., "version": ..., "name": ...}
        if a match is found, {"exists": False} if no match is found, or None if a query error occurs.
    """
    dt = get_delta_table(DATASETS_TABLE_PATH)
    if not dt:
        print("Datasets table not found for definition query.")
        # Return None to indicate system/query error, distinct from 'exists: False'
        return None

    # Hashes are automatically calculated by the Pydantic model validator now
    source_hash = query.query_source_sig_hash
    params_hash = query.query_params_sig_hash

    # Check if hashes were successfully generated (they are Optional in the model just in case)
    if not source_hash or not params_hash:
        print(
            f"Warning: Cannot query by definition because hashes could not be generated. "
            f"Source Hash: {source_hash}, Params Hash: {params_hash}"
        )
        # Cannot perform the efficient query without hashes
        return {"exists": False}

    print(
        f"Querying for dataset with source_sig_hash: {source_hash}, params_sig_hash: {params_hash}"
    )

    try:
        # --- Efficient Filter using Hashes ---
        filters = (
            (ds.field("source_sig_hash") == source_hash)
            & (ds.field("params_sig_hash") == params_hash)
            &
            # --- Ensure we only match usable datasets ---
            (ds.field("status") == "available")
        )

        # Execute the query against the Delta table
        table = dt.to_pyarrow_table(filters=filters)
        # ---

        if len(table) == 0:
            # No 'available' dataset matches the exact source versions and parameters.
            print("No matching available dataset found by definition.")
            return {"exists": False}
        else:
            # One or more matches found (ideally just one if status is managed correctly).
            # Find the latest one based on the commit timestamp.
            print(
                f"Found {len(table)} potential matches by definition. Selecting latest."
            )
            table = table.sort_by([("timestamp", "descending")])
            latest_match = table.to_pylist()[0]  # Get the most recent matching record

            # Return information about the latest existing match
            return {
                "exists": True,
                "dataset_id": latest_match.get("dataset_id"),
                # IMPORTANT: Returning dt.version() gives the latest version of the whole table,
                # not necessarily the version WHEN this specific dataset was committed.
                # For the SDK's purpose (finding *if* it exists and getting its ID),
                # returning the dataset_id and name is the most crucial part.
                # The actual version to load might be determined later if needed,
                # possibly by doing a get_dataset_info with the returned dataset_id.
                # Let's simplify the response for now.
                "version": latest_match.get(
                    "version", dt.version()
                ),  # Placeholder version concept
                "name": latest_match.get("name"),
            }

    except DeltaError as e:
        # Catch specific Delta Lake errors during query execution
        print(f"DeltaError finding dataset by definition: {e}")
        return None  # Indicate query failure
    except pa.lib.ArrowNotImplementedError as e:
        # Catch potential errors if filtering on hashes isn't fully supported by engine/bindings
        print(
            f"ArrowNotImplementedError finding dataset: Filtering on hashes might not be supported. {e}"
        )
        # In this case, we might have to revert to less efficient filtering as a fallback,
        # but for now, report failure.
        return None  # Indicate query failure
    except Exception as e:
        # Catch any other unexpected errors during the query
        print(f"Unexpected error finding dataset by definition: {e}")
        import traceback

        traceback.print_exc()
        return None  # Indicate query failure


# --- Add other functions later ---
# async def list_datasets(...) -> list[dict]: ...
# async def trace_dataset_lineage(dataset_id: str, version: int) -> list: ... (Implementation needs careful recursion/querying)
