"""Schema definitions for Metadata Service Delta tables."""

import pyarrow as pa

# Import shared schemas from canonical location
from tfw_raster_schemas import GRID_TEMPLATES_SCHEMA

# Schema for the 'jobs' Delta table
JOBS_SCHEMA = pa.schema([
    pa.field("job_id", pa.string()),
    pa.field("job_type", pa.string()),
    pa.field("status", pa.string()),
    pa.field("submit_time", pa.timestamp("us", tz="UTC")),
    pa.field("start_time", pa.timestamp("us", tz="UTC"), nullable=True),
    pa.field("end_time", pa.timestamp("us", tz="UTC"), nullable=True),
    pa.field("input_artifact_versions", pa.string()),  # JSON string
    pa.field("output_dataset_id", pa.string(), nullable=True),
    pa.field("parameters", pa.string()),  # JSON string
    pa.field("error_message", pa.string(), nullable=True),
    pa.field("compute_engine", pa.string()),
]) 