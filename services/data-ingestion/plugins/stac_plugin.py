# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
STAC ingestion plugin for satellite imagery data.
Supports Sentinel-2, Landsat, and other STAC collections.
Uses existing Terrafloww schemas and parsers.
"""

import os
import sys
import asyncio
import json
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime

from framework.base_plugin import BasePlugin, plugin_registry

# Import Terrafloww internal libraries
try:
    # Import schemas and configurations
    from tfw_raster_schemas.stac import EXT_STAC_IMG_DATASETS_SCHEMA, PARTITION_COLS, ASSETS_TO_PROCESS

    # Import COG parser
    from terrafloww.engine_core.parser import parse_cog_header_info

    # Import standard dependencies
    import pystac_client
    import pandas as pd
    import pyarrow as pa
    from deltalake import write_deltalake
    import httpx
    from shapely.geometry import shape as shapely_shape
    from pystac import Item

except ImportError as e:
    raise ImportError(f"Required Terrafloww dependencies not available: {e}")


class StacPlugin(BasePlugin):
    """Plugin for ingesting STAC (SpatioTemporal Asset Catalog) data using existing Terrafloww infrastructure."""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        # Get supported collections from the schema configuration
        self.supported_collections = list(ASSETS_TO_PROCESS.keys()) if 'ASSETS_TO_PROCESS' in globals() else [
            "sentinel-2-l2a",
            "landsat-c2-l2",
            "sentinel-1-grd"
        ]
    
    def validate_config(self) -> bool:
        """Validate STAC plugin configuration."""
        required_env_vars = [
            "STAC_CATALOG_S3_BUCKET",
            "STAC_CATALOG_S3_REGION", 
            "AWS_ACCESS_KEY_ID",
            "AWS_SECRET_ACCESS_KEY"
        ]
        
        missing_vars = [var for var in required_env_vars if not os.getenv(var)]
        if missing_vars:
            self.logger.error(f"Missing required environment variables: {missing_vars}")
            return False
        
        return True
    
    def get_schema(self) -> Dict[str, Any]:
        """Return parameter schema for STAC ingestion."""
        return {
            "type": "object",
            "properties": {
                "stac_api": {
                    "type": "string",
                    "description": "STAC API endpoint URL",
                    "default": "https://earth-search.aws.element84.com/v1"
                },
                "collection": {
                    "type": "string", 
                    "description": "STAC collection name",
                    "enum": self.supported_collections,
                    "default": "sentinel-2-l2a"
                },
                "bbox": {
                    "type": "array",
                    "description": "Bounding box [west, south, east, north]",
                    "items": {"type": "number"},
                    "minItems": 4,
                    "maxItems": 4
                },
                "datetime": {
                    "type": "string",
                    "description": "Date range in format YYYY-MM-DD/YYYY-MM-DD",
                    "pattern": r"^\d{4}-\d{2}-\d{2}/\d{4}-\d{2}-\d{2}$"
                },
                "max_items": {
                    "type": "integer",
                    "description": "Maximum number of STAC items to process",
                    "minimum": 1,
                    "maximum": 1000,
                    "default": 10
                },
                "batch_size": {
                    "type": "integer", 
                    "description": "Number of items to process per batch",
                    "minimum": 1,
                    "maximum": 100,
                    "default": 10
                },
                "limit": {
                    "type": "integer",
                    "description": "Items per STAC API request",
                    "minimum": 1,
                    "maximum": 100,
                    "default": 10
                }
            },
            "required": ["collection", "bbox", "datetime"]
        }
    
    def ingest(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute STAC data ingestion using existing Terrafloww infrastructure."""
        # Set default values
        stac_api = parameters.get("stac_api", "https://earth-search.aws.element84.com/v1")
        collection = parameters["collection"]
        bbox = parameters["bbox"]
        datetime_range = parameters["datetime"]
        max_items = parameters.get("max_items", 10)
        batch_size = parameters.get("batch_size", 10)
        limit = parameters.get("limit", 10)

        self.logger.info(f"Starting STAC ingestion for collection: {collection}")
        self.logger.info(f"STAC API: {stac_api}")
        self.logger.info(f"Bbox: {bbox}, DateTime: {datetime_range}")
        self.logger.info(f"Max items: {max_items}, Batch size: {batch_size}")

        try:
            # Setup S3 configuration
            s3_config = self._get_s3_config()
            delta_table_path = f"s3://{s3_config['bucket']}/{s3_config['path_prefix']}/ext_stac_datasets"

            self.logger.info(f"Output Delta Table: {delta_table_path}")
            self.logger.info("Using S3 storage backend for Delta Lake")

            # Run the async ingestion
            result = asyncio.run(self._async_ingest(
                stac_api=stac_api,
                collection=collection,
                bbox=bbox,
                datetime_range=datetime_range,
                max_items=max_items,
                batch_size=batch_size,
                limit=limit,
                delta_table_path=delta_table_path,
                s3_config=s3_config
            ))

            return result

        except Exception as e:
            self.logger.error(f"STAC ingestion failed: {str(e)}")
            raise
    
    def _get_s3_config(self) -> Dict[str, str]:
        """Get S3 configuration from environment variables."""
        return {
            "bucket": os.getenv("STAC_CATALOG_S3_BUCKET", "").strip(),
            "endpoint": os.getenv("STAC_CATALOG_S3_ENDPOINT", "https://s3.amazonaws.com").strip(),
            "region": os.getenv("STAC_CATALOG_S3_REGION", "us-east-1").strip(),
            "path_prefix": os.getenv("STAC_CATALOG_S3_PATH_PREFIX", "catalog").strip(),
            "access_key": os.getenv("AWS_ACCESS_KEY_ID", "").strip(),
            "secret_key": os.getenv("AWS_SECRET_ACCESS_KEY", "").strip()
        }

    async def _async_ingest(self, stac_api: str, collection: str, bbox: List[float],
                           datetime_range: str, max_items: int, batch_size: int,
                           limit: int, delta_table_path: str, s3_config: Dict[str, str]) -> Dict[str, Any]:
        """Async STAC ingestion using existing Terrafloww logic."""

        # Initialize STAC Client
        client = pystac_client.Client.open(stac_api)
        self.logger.info(f"Opened STAC client for {stac_api}")

        # Prepare search parameters
        search_params = {
            "collections": [collection],
            "limit": limit,
            "max_items": max_items,
            "bbox": bbox,
            "datetime": datetime_range
        }

        self.logger.info(f"Searching STAC with params: {search_params}")

        total_processed_items = 0
        total_cog_rows = 0
        processed_batches = 0

        # Configure HTTP client for COG header parsing
        limits = httpx.Limits(max_connections=50, max_keepalive_connections=50)
        timeout = httpx.Timeout(60.0, connect=10.0)

        async with httpx.AsyncClient(
            limits=limits,
            timeout=timeout,
            http2=True,
            follow_redirects=True,
            trust_env=True,
        ) as http_client:

            # Search for STAC items
            search = client.search(**search_params)
            item_iterator = search.items()

            batch_tasks = []
            batch_item_ids = []

            for item in item_iterator:
                if not isinstance(item, Item):
                    self.logger.warning(f"Received non-Item object: {type(item)}")
                    continue

                batch_tasks.append(
                    self._process_stac_item_to_cog_rows(item, collection, http_client)
                )
                batch_item_ids.append(item.id)

                if len(batch_tasks) >= batch_size:
                    processed_batches += 1
                    batch_result = await self._process_batch(
                        batch_tasks, batch_item_ids, processed_batches,
                        delta_table_path, s3_config
                    )
                    total_processed_items += batch_result["items_processed"]
                    total_cog_rows += batch_result["assets_processed"]

                    # Reset for next batch
                    batch_tasks = []
                    batch_item_ids = []

            # Process remaining items
            if batch_tasks:
                processed_batches += 1
                batch_result = await self._process_batch(
                    batch_tasks, batch_item_ids, processed_batches,
                    delta_table_path, s3_config
                )
                total_processed_items += batch_result["items_processed"]
                total_cog_rows += batch_result["assets_processed"]

        self.logger.info("Ingestion finished.")
        self.logger.info(f"Processed {total_processed_items} STAC items.")
        self.logger.info(f"Wrote {total_cog_rows} asset rows to {delta_table_path} across {processed_batches} batches.")

        return {
            "items_processed": total_processed_items,
            "assets_processed": total_cog_rows,
            "batches_processed": processed_batches,
            "delta_table_path": delta_table_path,
            "collection": collection,
            "stac_api": stac_api
        }
    


    async def _process_stac_item_to_cog_rows(self, item: Item, collection_id: str,
                                           http_client: httpx.AsyncClient) -> List[Dict[str, Any]]:
        """Process a single STAC item using existing Terrafloww logic."""
        cog_rows = []
        self.logger.debug(f"Processing item: {item.id}")

        try:
            # Extract Scene-Level Info (adapted from working script)
            scene_id = item.id
            dt_str = item.datetime.isoformat()
            scene_datetime = pd.Timestamp(dt_str).tz_convert("UTC")
            if scene_datetime.tzinfo is None:
                scene_datetime = scene_datetime.tz_localize("UTC")

            scene_geometry = shapely_shape(item.geometry) if item.geometry else None
            scene_geometry_wkt = scene_geometry.wkt if scene_geometry else None
            bbox = item.bbox or (scene_geometry.bounds if scene_geometry else None)

            if bbox and len(bbox) == 4:
                bbox_minx, bbox_miny, bbox_maxx, bbox_maxy = bbox
            else:
                bbox_minx, bbox_miny, bbox_maxx, bbox_maxy = None, None, None, None

            # Extract cloud cover
            cloud_cover = item.properties.get("eo:cloud_cover") or item.properties.get("cloud_cover")
            if not isinstance(cloud_cover, (int, float)):
                cloud_cover = None

            year = scene_datetime.year
            month = scene_datetime.month

            # Store original properties/assets as JSON
            stac_properties_json = json.dumps(item.properties)
            stac_assets_dict = {k: v.to_dict() for k, v in item.assets.items() if v.href}
            stac_assets_json = json.dumps(stac_assets_dict)

            # Identify and Parse Relevant COG Assets
            cog_keys_to_parse = ASSETS_TO_PROCESS.get(collection_id, [])
            tasks = []
            cog_metadata_map = {}

            for key in cog_keys_to_parse:
                if key in item.assets:
                    asset = item.assets[key]
                    cog_href = asset.get_absolute_href()

                    # Basic check for COG potential
                    is_cog_likely = (
                        cog_href and
                        (asset.media_type and "image/tiff" in asset.media_type.lower()) and
                        ("profile=cloud-optimized" in asset.media_type.lower() if asset.media_type else True)
                    )

                    if is_cog_likely:
                        self.logger.debug(f"Adding asset '{key}' for header parsing.")
                        tasks.append(self._parse_cog_header_wrapper(key, cog_href, http_client))
                        cog_metadata_map[key] = asset.to_dict()
                    else:
                        self.logger.debug(f"Skipping asset '{key}' (not COG or no href). Media type: {asset.media_type}")
                else:
                    self.logger.debug(f"Asset key '{key}' not found in item {scene_id}.")

            if not tasks:
                self.logger.warning(f"No suitable COG assets found for item {scene_id}. Skipping item.")
                return []

            self.logger.info(f"Parsing {len(tasks)} asset headers for item {scene_id}...")
            parsed_header_results = await asyncio.gather(*tasks)

            # Create rows for successfully parsed assets
            for cog_key, header_info in parsed_header_results:
                if header_info:
                    try:
                        original_cog_info = cog_metadata_map.get(cog_key, {})
                        cog_href = original_cog_info.get("href")

                        if not cog_href:
                            self.logger.warning(f"Missing href for asset '{cog_key}' in item {scene_id}. Skipping.")
                            continue

                        # Extract scale and offset from raster:bands extension
                        cog_scale = None
                        cog_offset = None
                        raster_bands = original_cog_info.get("raster:bands", [])
                        if raster_bands and isinstance(raster_bands, list) and len(raster_bands) > 0:
                            first_band = raster_bands[0]
                            if isinstance(first_band, dict):
                                cog_scale = first_band.get("scale")
                                cog_offset = first_band.get("offset")

                        # If not found in raster:bands, check asset properties
                        if cog_scale is None:
                            cog_scale = original_cog_info.get("scale")
                        if cog_offset is None:
                            cog_offset = original_cog_info.get("offset")

                        # Create row using existing schema structure
                        row = {
                            # Scene Info
                            "scene_id": scene_id,
                            "collection": collection_id,
                            "datetime": scene_datetime,
                            "year": year,
                            "month": month,
                            "geometry_wkt": scene_geometry_wkt,
                            "bbox_minx": bbox_minx,
                            "bbox_miny": bbox_miny,
                            "bbox_maxx": bbox_maxx,
                            "bbox_maxy": bbox_maxy,
                            "cloud_cover": cloud_cover,
                            "stac_properties_json": stac_properties_json,
                            "stac_assets_json": stac_assets_json,
                            # Asset Info
                            "cog_key": cog_key,
                            "cog_href": cog_href,
                            "cog_title": original_cog_info.get("title"),
                            "cog_roles": original_cog_info.get("roles"),
                            # Parsed COG Info
                            "cog_width": header_info["width"],
                            "cog_height": header_info["height"],
                            "cog_tile_width": header_info["tile_width"],
                            "cog_tile_height": header_info["tile_height"],
                            "cog_transform": header_info["transform"],
                            "cog_crs": header_info["crs_string"],
                            "cog_dtype": header_info["dtype_str"],
                            "cog_predictor": header_info["predictor"],
                            "cog_compression": header_info.get("compression_str"),
                            "cog_tile_offsets": header_info["tile_offsets"],
                            "cog_tile_byte_counts": header_info["tile_byte_counts"],
                            "cog_scale": float(cog_scale) if cog_scale is not None else None,
                            "cog_offset": float(cog_offset) if cog_offset is not None else None,
                        }
                        cog_rows.append(row)

                    except Exception as row_err:
                        self.logger.error(f"Error formatting row for asset '{cog_key}' in item {scene_id}: {row_err}", exc_info=True)
                else:
                    self.logger.warning(f"Skipping asset '{cog_key}' in item {scene_id} due to parsing failure.")

        except Exception as item_err:
            self.logger.error(f"Failed to process STAC item {getattr(item, 'id', 'UNKNOWN_ID')}: {item_err}", exc_info=True)
            return []

        if not cog_rows:
            self.logger.warning(f"No assets successfully processed for item {scene_id}.")

        return cog_rows

    async def _parse_cog_header_wrapper(self, cog_key: str, cog_href: str,
                                       http_client: httpx.AsyncClient) -> tuple[str, Optional[Dict[str, Any]]]:
        """Wrapper to call the internal parser and handle errors."""
        if not cog_href:
            self.logger.warning(f"Asset '{cog_key}' has no URL.")
            return cog_key, None

        try:
            self.logger.debug(f"Parsing header for {cog_key} @ {cog_href}")
            header_info = await parse_cog_header_info(client=http_client, url=cog_href)

            if header_info is None:
                self.logger.error(f"Failed to parse header for asset '{cog_key}' URL: {cog_href}")
                return cog_key, None

            # Ensure all required keys are present
            required_keys = [
                "width", "height", "tile_width", "tile_height", "transform",
                "crs_string", "dtype_str", "predictor", "tile_offsets", "tile_byte_counts"
            ]
            if not all(k in header_info for k in required_keys):
                self.logger.error(f"Parsed header for asset '{cog_key}' is missing required keys. Got: {header_info.keys()}")
                return cog_key, None

            # Add compression info if available
            header_info["compression_str"] = str(header_info.get("compression", "unknown"))

            return cog_key, header_info

        except httpx.HTTPStatusError as e:
            self.logger.error(f"HTTP error {e.response.status_code} parsing header for asset '{cog_key}' URL: {cog_href}")
            return cog_key, None
        except httpx.RequestError as e:
            self.logger.error(f"Request error parsing header for asset '{cog_key}' URL: {cog_href} - {e}")
            return cog_key, None
        except Exception as e:
            self.logger.exception(f"Unexpected error parsing header for asset '{cog_key}' URL: {cog_href}: {e}")
            return cog_key, None

    async def _process_batch(self, batch_tasks: List, batch_item_ids: List[str],
                           batch_num: int, delta_table_path: str,
                           s3_config: Dict[str, str]) -> Dict[str, int]:
        """Process a batch of STAC items and write to Delta Lake."""
        if not batch_tasks:
            return {"items_processed": 0, "assets_processed": 0}

        self.logger.info(f"Processing batch of {len(batch_tasks)} items (starting with {batch_item_ids[0]})...")

        # Execute all tasks in parallel
        batch_results_list = await asyncio.gather(*batch_tasks)

        # Flatten results (list of lists of dicts)
        all_rows_in_batch = [
            row for sublist in batch_results_list if sublist for row in sublist
        ]

        if not all_rows_in_batch:
            self.logger.info("No asset rows generated from this batch.")
            return {"items_processed": len(batch_tasks), "assets_processed": 0}

        self.logger.info(f"Writing {len(all_rows_in_batch)} asset rows to Delta table at {delta_table_path}...")

        try:
            # Create table using existing schema
            batch_table = pa.Table.from_pylist(all_rows_in_batch, schema=EXT_STAC_IMG_DATASETS_SCHEMA)

            # Apply schema correction (from working script)
            batch_table = self._ensure_table_schema(batch_table, EXT_STAC_IMG_DATASETS_SCHEMA, f"Batch {batch_num}")

            self.logger.info(f"Batch {batch_num}: Writing {len(batch_table)} rows to Delta table...")

            # Prepare storage options
            storage_options = {
                "AWS_ACCESS_KEY_ID": s3_config["access_key"],
                "AWS_SECRET_ACCESS_KEY": s3_config["secret_key"],
                "AWS_REGION": s3_config["region"],
                "AWS_S3_ALLOW_UNSAFE_RENAME": "true"
            }

            if s3_config["endpoint"] != "https://s3.amazonaws.com":
                storage_options["AWS_ENDPOINT_URL"] = s3_config["endpoint"]

            # Write to Delta Lake
            write_deltalake(
                table_or_uri=delta_table_path,
                data=batch_table,
                partition_by=PARTITION_COLS,
                mode="append",
                engine="rust",
                schema_mode="merge",
                storage_options=storage_options
            )

            self.logger.info(f"Successfully wrote batch {batch_num}.")

            return {"items_processed": len(batch_tasks), "assets_processed": len(all_rows_in_batch)}

        except Exception as write_err:
            self.logger.exception(f"Failed to write batch to Delta table at {delta_table_path}: {write_err}")
            raise

    def _ensure_table_schema(self, input_table: pa.Table, target_schema: pa.Schema,
                           table_description: str = "Input Table") -> pa.Table:
        """Ensure table schema matches target schema (adapted from working script)."""
        self.logger.debug(f"--- Ensuring Schema for {table_description} ---")

        if input_table.schema.equals(target_schema):
            self.logger.debug(f"Schema for {table_description} already matches target.")
            return input_table

        self.logger.info(f"Rebuilding table for {table_description} to match target schema.")

        corrected_arrays = []
        for field in target_schema:
            col_name = field.name
            target_type = field.type

            try:
                current_column = input_table.column(col_name)
                if not current_column.type.equals(target_type):
                    self.logger.info(f"Casting '{col_name}' from {current_column.type} to {target_type}")

                    # Special handling for list fields to ensure correct element naming
                    if pa.types.is_list(target_type) and pa.types.is_list(current_column.type):
                        # For list fields, recreate the array with correct element field name
                        list_values = current_column.to_pylist()
                        casted_column = pa.array(list_values, type=target_type)
                        corrected_arrays.append(casted_column)
                    else:
                        casted_column = current_column.cast(target_type)
                        corrected_arrays.append(casted_column)
                else:
                    corrected_arrays.append(current_column)

            except KeyError:
                self.logger.error(f"Column '{col_name}' missing in source table.")
                raise ValueError(f"Cannot create table: Column '{col_name}' missing.")
            except Exception as e:
                self.logger.error(f"Failed to cast column '{col_name}' to type {target_type}: {e}")
                raise ValueError(f"Failed casting column '{col_name}'") from e

        try:
            final_table = pa.Table.from_arrays(corrected_arrays, schema=target_schema)
            self.logger.info(f"Successfully rebuilt table for {table_description} matching target schema.")
            return final_table
        except Exception as e:
            self.logger.error(f"Failed to create final table from corrected arrays: {e}")
            raise ValueError("Failed to create final table after casting") from e


# Register the plugin
plugin_registry.register(StacPlugin)
