# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Base plugin interface for data ingestion framework.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import logging
import json
import os
from datetime import datetime


class BasePlugin(ABC):
    """Base class for all data ingestion plugins."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize plugin with configuration."""
        self.config = config
        self.plugin_name = self.__class__.__name__.replace('Plugin', '').lower()
        self.logger = self._setup_logging()
        
    @abstractmethod
    def validate_config(self) -> bool:
        """
        Validate plugin configuration.
        
        Returns:
            bool: True if configuration is valid, False otherwise
        """
        pass
    
    @abstractmethod
    def ingest(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute data ingestion with given parameters.
        
        Args:
            parameters: Ingestion parameters specific to this plugin
            
        Returns:
            Dict containing ingestion results and metadata
        """
        pass
    
    @abstractmethod
    def get_schema(self) -> Dict[str, Any]:
        """
        Return parameter schema for this plugin.
        
        Returns:
            JSON schema describing expected parameters
        """
        pass
    
    def get_plugin_info(self) -> Dict[str, Any]:
        """
        Get plugin information and metadata.
        
        Returns:
            Dict containing plugin name, version, description, etc.
        """
        return {
            "name": self.plugin_name,
            "class": self.__class__.__name__,
            "description": self.__doc__ or "No description available",
            "schema": self.get_schema()
        }
    
    def _setup_logging(self) -> logging.Logger:
        """Setup structured logging for the plugin."""
        logger = logging.getLogger(f"ingestion.{self.plugin_name}")
        
        # Configure structured logging
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = StructuredFormatter()
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        # Set log level from environment or config
        log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        logger.setLevel(getattr(logging, log_level))
        
        return logger
    
    def _log_ingestion_start(self, parameters: Dict[str, Any]) -> str:
        """Log ingestion start and return correlation ID."""
        correlation_id = f"{self.plugin_name}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        self.logger.info(
            "Ingestion started",
            extra={
                "plugin": self.plugin_name,
                "correlation_id": correlation_id,
                "parameters": parameters,
                "event": "ingestion_start"
            }
        )
        
        return correlation_id
    
    def _log_ingestion_complete(self, correlation_id: str, result: Dict[str, Any]):
        """Log successful ingestion completion."""
        self.logger.info(
            "Ingestion completed successfully",
            extra={
                "plugin": self.plugin_name,
                "correlation_id": correlation_id,
                "result": result,
                "event": "ingestion_complete"
            }
        )
    
    def _log_ingestion_error(self, correlation_id: str, error: Exception):
        """Log ingestion error."""
        self.logger.error(
            f"Ingestion failed: {str(error)}",
            extra={
                "plugin": self.plugin_name,
                "correlation_id": correlation_id,
                "error": str(error),
                "error_type": type(error).__name__,
                "event": "ingestion_error"
            },
            exc_info=True
        )
    
    def execute_with_logging(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute ingestion with automatic logging and error handling.
        
        Args:
            parameters: Ingestion parameters
            
        Returns:
            Dict containing ingestion results
            
        Raises:
            Exception: Re-raises any exception that occurs during ingestion
        """
        correlation_id = self._log_ingestion_start(parameters)
        
        try:
            # Validate configuration before execution
            if not self.validate_config():
                raise ValueError(f"Invalid configuration for {self.plugin_name} plugin")
            
            # Execute ingestion
            result = self.ingest(parameters)
            
            # Add metadata to result
            result.update({
                "plugin": self.plugin_name,
                "correlation_id": correlation_id,
                "timestamp": datetime.utcnow().isoformat(),
                "status": "success"
            })
            
            self._log_ingestion_complete(correlation_id, result)
            return result
            
        except Exception as e:
            self._log_ingestion_error(correlation_id, e)
            
            # Return error result instead of raising
            return {
                "plugin": self.plugin_name,
                "correlation_id": correlation_id,
                "timestamp": datetime.utcnow().isoformat(),
                "status": "error",
                "error": str(e),
                "error_type": type(e).__name__
            }


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured JSON logging."""
    
    def format(self, record):
        """Format log record as structured JSON."""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
        }
        
        # Add extra fields if present
        if hasattr(record, 'plugin'):
            log_entry["plugin"] = record.plugin
        if hasattr(record, 'correlation_id'):
            log_entry["correlation_id"] = record.correlation_id
        if hasattr(record, 'event'):
            log_entry["event"] = record.event
        if hasattr(record, 'parameters'):
            log_entry["parameters"] = record.parameters
        if hasattr(record, 'result'):
            log_entry["result"] = record.result
        if hasattr(record, 'error'):
            log_entry["error"] = record.error
        if hasattr(record, 'error_type'):
            log_entry["error_type"] = record.error_type
            
        return json.dumps(log_entry)


class PluginRegistry:
    """Registry for managing available plugins."""
    
    def __init__(self):
        self._plugins = {}
    
    def register(self, plugin_class: type):
        """Register a plugin class."""
        plugin_name = plugin_class.__name__.replace('Plugin', '').lower()
        self._plugins[plugin_name] = plugin_class
    
    def get_plugin(self, name: str, config: Dict[str, Any]) -> BasePlugin:
        """Get plugin instance by name."""
        if name not in self._plugins:
            raise ValueError(f"Unknown plugin: {name}")
        
        return self._plugins[name](config)
    
    def list_plugins(self) -> Dict[str, Dict[str, Any]]:
        """List all registered plugins with their info."""
        plugins_info = {}
        for name, plugin_class in self._plugins.items():
            # Create temporary instance to get info
            temp_instance = plugin_class({})
            plugins_info[name] = temp_instance.get_plugin_info()
        
        return plugins_info


# Global plugin registry
plugin_registry = PluginRegistry()
