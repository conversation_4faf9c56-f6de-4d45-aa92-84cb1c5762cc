# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Main entry point for the data ingestion framework.
Executes plugins based on command line arguments or environment variables.
"""

import os
import sys
import json
import argparse
from typing import Dict, Any
from pathlib import Path

# Add framework and plugins to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from framework.base_plugin import plugin_registry
from plugins.stac_plugin import StacPlugin  # Import to register


def load_config() -> Dict[str, Any]:
    """Load configuration from environment variables and config files."""
    config = {}
    
    # Load from environment variables
    config.update({
        "log_level": os.getenv("LOG_LEVEL", "INFO"),
        "s3_bucket": os.getenv("STAC_CATALOG_S3_BUCKET"),
        "s3_region": os.getenv("STAC_CATALOG_S3_REGION"),
        "s3_endpoint": os.getenv("STAC_CATALOG_S3_ENDPOINT"),
        "s3_path_prefix": os.getenv("STAC_CATALOG_S3_PATH_PREFIX", "catalog"),
        "aws_access_key": os.getenv("AWS_ACCESS_KEY_ID"),
        "aws_secret_key": os.getenv("AWS_SECRET_ACCESS_KEY")
    })
    
    return config


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Data Ingestion Framework",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # STAC ingestion
  python main.py stac --collection sentinel-2-l2a \\
    --bbox="-122.5,37.7,-122.3,37.9" \\
    --datetime="2024-06-01/2024-06-25" \\
    --max-items=10

  # List available plugins
  python main.py --list-plugins

  # Get plugin schema
  python main.py --plugin-schema stac
        """
    )
    
    parser.add_argument(
        "plugin",
        nargs="?",
        help="Plugin name to execute"
    )
    
    parser.add_argument(
        "--list-plugins",
        action="store_true",
        help="List all available plugins"
    )
    
    parser.add_argument(
        "--plugin-schema",
        metavar="PLUGIN",
        help="Show parameter schema for a plugin"
    )
    
    # STAC plugin arguments
    parser.add_argument(
        "--stac-api",
        default="https://earth-search.aws.element84.com/v1",
        help="STAC API endpoint URL"
    )
    
    parser.add_argument(
        "--collection",
        help="STAC collection name (required for STAC plugin)"
    )
    
    parser.add_argument(
        "--bbox",
        help="Bounding box as 'west,south,east,north'"
    )
    
    parser.add_argument(
        "--datetime",
        help="Date range as 'YYYY-MM-DD/YYYY-MM-DD'"
    )
    
    parser.add_argument(
        "--max-items",
        type=int,
        default=10,
        help="Maximum number of items to process"
    )
    
    parser.add_argument(
        "--batch-size",
        type=int,
        default=10,
        help="Number of items per batch"
    )
    
    parser.add_argument(
        "--limit",
        type=int,
        default=10,
        help="Items per API request"
    )
    
    parser.add_argument(
        "--parameters",
        help="JSON string with plugin parameters"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level"
    )
    
    return parser.parse_args()


def list_plugins():
    """List all available plugins."""
    plugins = plugin_registry.list_plugins()
    
    print("Available Plugins:")
    print("=" * 50)
    
    for name, info in plugins.items():
        print(f"\nPlugin: {name}")
        print(f"Class: {info['class']}")
        print(f"Description: {info['description']}")


def show_plugin_schema(plugin_name: str):
    """Show parameter schema for a plugin."""
    try:
        config = load_config()
        plugin = plugin_registry.get_plugin(plugin_name, config)
        schema = plugin.get_schema()
        
        print(f"Parameter Schema for '{plugin_name}' Plugin:")
        print("=" * 50)
        print(json.dumps(schema, indent=2))
        
    except ValueError as e:
        print(f"Error: {e}")
        sys.exit(1)


def build_parameters(args) -> Dict[str, Any]:
    """Build parameters dictionary from command line arguments."""
    if args.parameters:
        # Parse JSON parameters
        try:
            return json.loads(args.parameters)
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON parameters: {e}")
            sys.exit(1)
    
    # Build parameters from individual arguments
    parameters = {}
    
    if args.plugin == "stac":
        if not args.collection:
            print("Error: --collection is required for STAC plugin")
            sys.exit(1)
        if not args.bbox:
            print("Error: --bbox is required for STAC plugin")
            sys.exit(1)
        if not args.datetime:
            print("Error: --datetime is required for STAC plugin")
            sys.exit(1)
        
        # Parse bbox
        try:
            bbox = [float(x.strip()) for x in args.bbox.split(",")]
            if len(bbox) != 4:
                raise ValueError("Bbox must have exactly 4 values")
        except ValueError as e:
            print(f"Error parsing bbox: {e}")
            sys.exit(1)
        
        parameters = {
            "stac_api": args.stac_api,
            "collection": args.collection,
            "bbox": bbox,
            "datetime": args.datetime,
            "max_items": args.max_items,
            "batch_size": args.batch_size,
            "limit": args.limit
        }
    
    return parameters


def main():
    """Main entry point."""
    args = parse_arguments()
    
    # Set log level
    os.environ["LOG_LEVEL"] = args.log_level
    
    # Handle special commands
    if args.list_plugins:
        list_plugins()
        return
    
    if args.plugin_schema:
        show_plugin_schema(args.plugin_schema)
        return
    
    # Validate plugin argument
    if not args.plugin:
        print("Error: Plugin name is required")
        print("Use --list-plugins to see available plugins")
        sys.exit(1)
    
    # Load configuration
    config = load_config()
    
    # Validate required environment variables
    if not config["s3_bucket"]:
        print("Error: STAC_CATALOG_S3_BUCKET environment variable is required")
        sys.exit(1)
    
    if not config["aws_access_key"] or not config["aws_secret_key"]:
        print("Error: AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables are required")
        sys.exit(1)
    
    try:
        # Get plugin instance
        plugin = plugin_registry.get_plugin(args.plugin, config)
        
        # Build parameters
        parameters = build_parameters(args)
        
        print(f"Executing {args.plugin} plugin with parameters:")
        print(json.dumps(parameters, indent=2))
        print()
        
        # Execute plugin with logging
        result = plugin.execute_with_logging(parameters)
        
        # Print results
        print("\nIngestion Results:")
        print("=" * 50)
        print(json.dumps(result, indent=2))
        
        # Exit with appropriate code
        if result.get("status") == "error":
            sys.exit(1)
        else:
            print("\n✅ Ingestion completed successfully!")
            sys.exit(0)
            
    except ValueError as e:
        print(f"Error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
