# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Multi-stage Dockerfile for data ingestion service with platform dependencies

# Stage 1: Build platform libraries
FROM python:3.12-slim AS builder

# Install UV for fast Python package management
COPY --from=ghcr.io/astral-sh/uv:0.7.13 /uv /uvx /usr/local/bin/

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    build-essential \
    libgdal-dev \
    libproj-dev \
    libgeos-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /build

# Copy platform library source code
COPY libs/tfw_engine_core /build/tfw_engine_core
COPY libs/tfw_core_utils /build/tfw_core_utils
COPY libs/tfw_raster_schemas /build/tfw_raster_schemas

# Build wheels for platform libraries using UV
RUN --mount=type=cache,target=/root/.cache/uv \
    cd /build/tfw_engine_core && uv build --wheel --out-dir /wheels
RUN --mount=type=cache,target=/root/.cache/uv \
    cd /build/tfw_core_utils && uv build --wheel --out-dir /wheels
RUN --mount=type=cache,target=/root/.cache/uv \
    cd /build/tfw_raster_schemas && uv build --wheel --out-dir /wheels

# Stage 2: Final application image
FROM python:3.12-slim

# Install UV for fast Python package management
COPY --from=ghcr.io/astral-sh/uv:0.7.13 /uv /uvx /usr/local/bin/

# Install system dependencies for geospatial processing
RUN apt-get update && apt-get install -y \
    libgdal-dev \
    libproj-dev \
    libgeos-dev \
    libspatialindex-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy pre-built wheels from builder stage
COPY --from=builder /wheels /tmp/wheels

# Copy pyproject.toml and uv.lock
COPY services/data-ingestion/pyproject.toml services/data-ingestion/uv.lock ./

# Install remaining dependencies using uv first to create virtual environment
RUN uv sync --frozen

# Install platform libraries from wheels into the virtual environment
RUN --mount=type=cache,target=/root/.cache/uv \
    uv pip install --no-cache --find-links /tmp/wheels \
    tfw-engine-core \
    tfw-core-utils \
    tfw-raster-schemas

# Clean up wheels to reduce image size
RUN rm -rf /tmp/wheels

# Copy application code
COPY services/data-ingestion/framework/ ./framework/
COPY services/data-ingestion/plugins/ ./plugins/
COPY services/data-ingestion/consumers/ ./consumers/
COPY services/data-ingestion/main.py .

# Create non-root user
RUN useradd --create-home --shell /bin/bash ingestion
RUN chown -R ingestion:ingestion /app
USER ingestion

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PATH="/app/.venv/bin:$PATH"

# Default command - use virtual environment directly
ENTRYPOINT ["/app/.venv/bin/python", "main.py"]
CMD ["--help"]
