[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "tfw-processing-api"
version = "0.1.0"
description = "Generated gRPC/Protobuf code for Terrafloww Processing Engine API v1"
requires-python = ">=3.10"
dependencies = [
    "grpcio>=1.50.0",
    "protobuf>=4.0",
    "googleapis-common-protos", # Often needed
]

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]
include = ["terrafloww.*"]