# services/processing_engine/app/geo_ray_datasource.py

import os
import logging
from typing import Dict, List, Optional, Any, Itera<PERSON>, <PERSON><PERSON>, Union
import numpy as np
import httpx

import pyarrow as pa
import pyarrow.compute as pc
from ray.data.block import Block, BlockMetadata
from ray.data.datasource import Datasource, Reader, ReadTask
from shapely.geometry import shape

from terrafloww.engine_core.fetch import fetch_raw_window_data
from terrafloww.engine_core.process import process_raw_window_to_intermediate, process_raw_window_batch
from terrafloww.engine_core.grid import get_intersecting_tiles_and_windows
from tfw_raster_schemas.raster import RASTER_CHUNK_SCHEMA, INTERMEDIATE_CHUNK_SCHEMA

logger = logging.getLogger(__name__)

class GeoImageRayDataSource(Datasource):
    """
    Ray datasource for efficient loading of geospatial data from COGs.
    """
    
    def create_reader(
        self,
        asset_table: pa.Table,
        aoi: Optional[Any] = None,  # Shapely geometry
        aoi_crs: Optional[str] = None,
        bands: Optional[List[str]] = None,
        **kwargs
    ) -> Reader:
        """
        Create a reader for the datasource.
        
        Args:
            asset_table: Table of COG assets from catalog
            aoi: Area of interest (Shapely geometry)
            aoi_crs: CRS of the AOI
            bands: List of bands to load
            **kwargs: Additional parameters
            
        Returns:
            Reader for the datasource
        """
        return GeoImageRayReader(asset_table, aoi, aoi_crs, bands, **kwargs)
    
    def do_write(self, blocks, metadata, ray_remote_args, path):
        """Not implemented for this datasource."""
        raise NotImplementedError("Write operation not supported for GeoRayDataSource")
    
    def on_write_complete(self, write_results):
        """Not implemented for this datasource."""
        pass


class GeoImageRayReader(Reader):
    """
    Reader for the GeoImageRayDataSource.
    """
    
    def __init__(
        self,
        asset_table: pa.Table,
        aoi: Optional[Any] = None,  # Shapely geometry
        aoi_crs: Optional[str] = None,
        bands: Optional[List[str]] = None,
        window_size: int = 256,
        **kwargs
    ):
        """
        Initialize the reader.
        
        Args:
            asset_table: Table of COG assets from catalog
            aoi: Area of interest (Shapely geometry)
            aoi_crs: CRS of the AOI
            bands: List of bands to load
            window_size: Size of windows to load
            **kwargs: Additional parameters
        """
        self.asset_table = asset_table
        self.aoi = aoi
        self.aoi_crs = aoi_crs
        self.bands = bands
        self.window_size = window_size
        self.kwargs = kwargs
    
    def estimate_inmemory_data_size(self) -> Optional[int]:
        """
        Estimate the size of data in memory.
        
        Returns:
            Estimated size in bytes or None
        """
        # Rough estimate based on asset table
        if 'cog_width' in self.asset_table.column_names and 'cog_height' in self.asset_table.column_names:
            # Calculate total pixels
            total_pixels = 0
            bands_count = len(self.bands) if self.bands else 3  # Default to 3 bands
            
            for i in range(len(self.asset_table)):
                width = self.asset_table['cog_width'][i].as_py()
                height = self.asset_table['cog_height'][i].as_py()
                
                # If AOI is provided, estimate the proportion of the image covered
                if self.aoi:
                    # Simple estimate - assume 25% coverage
                    # In a real implementation, calculate this more accurately
                    coverage = 0.25
                else:
                    coverage = 1.0
                
                total_pixels += width * height * coverage
            
            # Assume 4 bytes per pixel (float32)
            return total_pixels * bands_count * 4
        
        return None
    
    def get_read_tasks(self, parallelism: int) -> List[ReadTask]:
        """
        Get the read tasks for the datasource.
        
        Args:
            parallelism: Number of read tasks to create
            
        Returns:
            List of read tasks
        """
        # Group assets by scene for more efficient loading
        scenes = {}
        
        for i in range(len(self.asset_table)):
            scene_id = self.asset_table['scene_id'][i].as_py()
            
            if scene_id not in scenes:
                scenes[scene_id] = []
            
            scenes[scene_id].append(i)
        
        # Create read tasks - one per scene
        read_tasks = []
        
        for scene_id, asset_indices in scenes.items():
            # Create a read task for this scene
            scene_assets = self.asset_table.take(asset_indices)
            
            # Create a read task
            read_task = ReadTask(
                lambda s=scene_assets, a=self.aoi, c=self.aoi_crs, b=self.bands, w=self.window_size:
                    self._read_scene(s, a, c, b, w),
                BlockMetadata(
                    num_rows=None,
                    size_bytes=None,
                    schema=None,
                    input_files=None
                )
            )
            
            read_tasks.append(read_task)
        
        return read_tasks
    
    def _read_scene(
        self,
        scene_assets: pa.Table,
        aoi: Optional[Any],
        aoi_crs: Optional[str],
        bands: Optional[List[str]],
        window_size: int
    ) -> Iterator[Block]:
        """
        Read data from a scene.
        
        Args:
            scene_assets: Assets for a single scene
            aoi: Area of interest
            aoi_crs: CRS of the AOI
            bands: List of bands to load
            window_size: Size of windows to load
            
        Yields:
            Blocks of data
        """
        # Filter assets by band if requested
        if bands:
            # Filter to only include requested bands
            filtered_indices = []
            
            for i in range(len(scene_assets)):
                cog_key = scene_assets['cog_key'][i].as_py()
                
                if cog_key in bands:
                    filtered_indices.append(i)
            
            if filtered_indices:
                scene_assets = scene_assets.take(filtered_indices)
            else:
                logger.warning(f"No requested bands found in scene assets")
                return
        
        # Create HTTP client for COG requests
        with httpx.AsyncClient() as client:
            # Process scene assets
            async def process_scene():
                # For each asset, determine tiles and windows
                all_window_definitions = []
                
                for i in range(len(scene_assets)):
                    asset = {field: scene_assets[field][i].as_py() for field in scene_assets.column_names}
                    
                    # Create grid definition for this asset
                    grid_definition = {
                        "transform": [
                            asset['cog_transform'][0],
                            asset['cog_transform'][1],
                            asset['cog_transform'][2],
                            asset['cog_transform'][3],
                            asset['cog_transform'][4],
                            asset['cog_transform'][5]
                        ],
                        "width": asset['cog_width'],
                        "height": asset['cog_height'],
                        "tile_width": asset['cog_tile_width'],
                        "tile_height": asset['cog_tile_height']
                    }
                    
                    # Get intersecting tiles and windows
                    try:
                        intersecting_tiles = get_intersecting_tiles_and_windows(
                            grid_definition=grid_definition,
                            aoi=aoi
                        )
                    except Exception as e:
                        logger.error(f"Error getting intersecting tiles: {e}")
                        continue
                    
                    # For each tile, create a window definition
                    for tile in intersecting_tiles:
                        tile_r = tile['tile_r']
                        tile_c = tile['tile_c']
                        window = tile['window']
                        
                        # Calculate offsets using COG tile offsets
                        tile_index = tile_r * ((asset['cog_width'] + asset['cog_tile_width'] - 1) // asset['cog_tile_width']) + tile_c
                        
                        if tile_index < len(asset['cog_tile_offsets']):
                            offset = asset['cog_tile_offsets'][tile_index]
                            size = asset['cog_tile_byte_counts'][tile_index]
                            
                            # Create window definition for this tile
                            window_def = {
                                'scene_id': asset['scene_id'],
                                'cog_key': asset['cog_key'],
                                'cog_href': asset['cog_href'],
                                'tile_r': tile_r,
                                'tile_c': tile_c,
                                'offset': offset,
                                'size': size,
                                'window_col_off': window.col_off,
                                'window_row_off': window.row_off,
                                'window_width': window.width,
                                'window_height': window.height,
                                'tile_shape_decode_w': asset['cog_tile_width'],
                                'tile_shape_decode_h': asset['cog_tile_height'],
                                'expected_shape_w': window.width,
                                'expected_shape_h': window.height,
                                'dtype_str': asset['cog_dtype'],
                                'predictor': asset.get('cog_predictor', 1),
                                'scale': asset.get('cog_scale', 1.0),
                                'offset_proc': asset.get('cog_offset', 0.0),
                                'ref_transform_0': asset['cog_transform'][0],
                                'ref_transform_1': asset['cog_transform'][1],
                                'ref_transform_2': asset['cog_transform'][2],
                                'ref_transform_3': asset['cog_transform'][3],
                                'ref_transform_4': asset['cog_transform'][4],
                                'ref_transform_5': asset['cog_transform'][5],
                                'crs': asset['cog_crs'],
                                'datetime': asset.get('datetime', None)
                            }
                            
                            all_window_definitions.append(window_def)
                
                # If no window definitions, return
                if not all_window_definitions:
                    logger.warning("No window definitions created")
                    return
                
                # Batch window definitions for more efficient processing
                batch_size = 10  # Process 10 windows at a time
                batches = [
                    all_window_definitions[i:i+batch_size]
                    for i in range(0, len(all_window_definitions), batch_size)
                ]
                
                # Process each batch
                for batch in batches:
                    # Create PyArrow table from batch
                    batch_table = pa.Table.from_pylist(batch, schema=WINDOW_DEFINITION_SCHEMA)
                    
                    # Extract URLs and offsets
                    window_requests = []
                    for i in range(len(batch_table)):
                        window_requests.append({
                            'offset': batch_table['offset'][i].as_py(),
                            'size': batch_table['size'][i].as_py()
                        })
                    
                    # Fetch raw data
                    url = batch_table['cog_href'][0].as_py()  # Assuming all from same COG
                    raw_bytes = await fetch_raw_window_data(client, url, window_requests)
                    
                    # Process raw data
                    processed_batch = process_raw_window_batch(raw_bytes, batch_table)
                    
                    if processed_batch is not None:
                        # Yield the processed batch
                        yield processed_batch
            
            # Run the async function
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                for batch in loop.run_until_complete(process_scene()):
                    yield batch
            finally:
                loop.close()