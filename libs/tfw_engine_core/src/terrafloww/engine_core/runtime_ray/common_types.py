# libs/tfw_engine_core/src/terrafloww/engine_core/runtime_ray/common_types.py

from dataclasses import dataclass
from typing import Tuple, Optional
import logging, sys
# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", stream=sys.stdout
)
logger = logging.getLogger(__name__)

@dataclass(frozen=True) # frozen=True makes it immutable and hashable, good for data carriers
class WindowSpec:
    """
    Specifies a single window of data to be fetched and processed by a worker.
    Contains all necessary information extracted from the catalog and planned by the planner.
    """
    # --- Identification & Source ---
    cog_href: str               # URL of the source COG asset
    scene_id: str               # Original scene ID
    catalog_band_name: str      # Original band name from catalog (e.g., "B04", "B08")
    operation_band_name: str    # Band name used in operations (e.g., "red", "nir")
    collection: str             # Name of the collection (e.g., "sentinel-2-l2a")

    # --- Fetching Parameters ---
    byte_offset: int            # Byte offset within the COG file for the tile
    byte_size: int              # Size of the tile data in bytes

    # --- Window Geometry (Pixel Coordinates relative to *original* COG) ---
    # These define the slice to take *after* decoding the full tile
    window_col_off: int         # Column offset of the window within the full COG grid
    window_row_off: int         # Row offset of the window within the full COG grid
    window_width: int           # Width of the window to extract
    window_height: int          # Height of the window to extract

    # --- Tile Context (Needed for Decoding/Processing) ---
    tile_r: int                 # Tile row index (for logging/grouping)
    tile_c: int                 # Tile column index (for logging/grouping)
    tile_shape_decode_h: int    # Full height of the internal tile for predictor/decode
    tile_shape_decode_w: int    # Full width of the internal tile for predictor/decode

    # --- CRS & Transform (Needed for geo-referencing results) ---
    cog_crs: str                # CRS string of the source COG
    # Store affine elements directly for easier serialization than Affine object
    cog_transform_elements: Tuple[float, float, float, float, float, float] # GDAL order (c, a, b, f, d, e)

    # --- Processing Parameters ---
    dtype_str: str              # Numpy dtype string for decoding (e.g., "uint16")
    predictor: int              # TIFF predictor value (e.g., 1 for none, 2 for horizontal)
    scale_factor: float         # Scale factor to apply after decoding
    offset_factor: float        # Offset factor to apply after decoding

    # --- Temporal Information ---
    # Use ISO string for easy serialization; worker can parse if needed
    datetime_utc_iso: Optional[str] = None

    def __post_init__(self):
        # Basic validation after initialization
        if self.window_width <= 0 or self.window_height <= 0:
            raise ValueError(
                f"Window dimensions must be positive for WindowSpec associated with "
                f"href={self.cog_href}, band={self.catalog_band_name}, tile=({self.tile_r},{self.tile_c}). "
                f"Got width={self.window_width}, height={self.window_height}"
            )
        # Optional: Add a warning for zero byte size, but don't raise error yet.
        if self.byte_size <= 0:
            logger.warning(
                f"WindowSpec created with non-positive byte_size ({self.byte_size}) for "
                f"href={self.cog_href}, band={self.catalog_band_name}, tile=({self.tile_r},{self.tile_c}). "
                f"This might indicate an empty tile or catalog issue."
            )