# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# libs/tfw_engine_core/src/terrafloww/engine_core/runtime_ray/worker.py

import logging
import asyncio
import os
import sys
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict
import uuid
import pyarrow.flight as flight

import pyarrow as pa
import pyarrow.flight as flight
import numpy as np

# Add random module to imports if not already present
import random  # Add this at the top with other imports
import httpx
from rasterio.transform import Affine
from rasterio.windows import Window # Import Window
from datetime import datetime

# Import core engine components
from terrafloww.engine_core import fetch
# Import process - THIS IMPORT TRIGGERS @register in process.py
from terrafloww.engine_core import process
from terrafloww.engine_core import grid
from terrafloww.engine_core import utils as engine_utils
# Import the registry itself
from terrafloww.engine_core.registry import get as get_kernel_function, FUNCTIONS as KERNEL_REGISTRY # Import registry conten

# Import the WindowSpec dataclass and target output schema
from .common_types import WindowSpec
from tfw_raster_schemas.raster import RASTER_CHUNK_SCHEMA

# Ray Core import
import ray

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", stream=sys.stdout
)
logger = logging.getLogger(__name__)

DEFAULT_WORKER_RESOURCES = {"num_cpus": 1}

def _upload_batch_to_flight_server(execution_id: str, batch: pa.RecordBatch, flight_address: str) -> bool:
    """
    Upload a batch to the Flight server via do_put.

    Args:
        execution_id: Execution ID for tracking in Flight server
        batch: Arrow RecordBatch to upload
        flight_address: Flight server address (host:port format)

    Returns:
        bool: True if upload succeeded, False if it failed
    """
    try:
        # --- MODIFIED: Use the provided address directly ---
        flight_uri = f"grpc+tcp://{flight_address}"
        logger.info(f"Streaming results via Flight to given address: {flight_uri}")

        logger.info(f"Uploading batch with {batch.num_rows} rows for execution {execution_id}")

        # Connect to Flight server
        client = flight.FlightClient(flight_uri)
        logger.info(f"Connected to Flight server at {flight_uri}")

        # Create descriptor with execution_id as command
        descriptor = flight.FlightDescriptor.for_command(execution_id.encode())
        logger.info(f"Created descriptor for execution_id: {execution_id}")

        # Convert batch to table for upload
        table = pa.Table.from_batches([batch])
        logger.info(f"Created table with {table.num_rows} rows and schema: {table.schema}")

        # Upload the table via do_put
        logger.info("Starting do_put call...")
        writer, _ = client.do_put(descriptor, table.schema)
        logger.info("Got writer from do_put, writing table...")
        writer.write_table(table)
        logger.info("Wrote table, closing writer...")
        writer.close()
        logger.info("Writer closed successfully")

        logger.info(f"Successfully wrote batch of {batch.num_rows} rows to Flight")
        return True

    except Exception as e:
        logger.error(f"Failed to upload batch to Flight server at {flight_address}: {e}", exc_info=True)
        return False

@ray.remote(**DEFAULT_WORKER_RESOURCES)
def process_batch_on_worker(
    window_specs_batch: List[WindowSpec],
    plan_apply_steps_serializable: List[Dict[str, Any]],
    execution_id: str,
    flight_address: str  # <-- NEW: Accept the address
) -> bool:
    """
    Ray remote task to process a batch of window specs and write to Flight server.
    Returns True on success, raises an exception on failure.

    Args:
        window_specs_batch: A list of WindowSpec objects for this worker task.
        plan_apply_steps_serializable: List of dictionaries defining apply steps.
        execution_id: Execution ID for tracking in Flight server.

    Returns:
        True on success, raises exception on failure
    """
    
    async def _run_async_logic():
        # Log the start of processing
        logger.info(f"Starting async processing for {len(window_specs_batch)} window specs")
        if not window_specs_batch:
            logger.warning("Worker received an empty batch of window specs.")
            return False

        logger.info(f"Worker async logic starting for {len(window_specs_batch)} window specs.")

        # Check if HTTP actor pool is enabled
        use_http_pool = os.environ.get("TFW_USE_HTTP_POOL", "false").lower() == "true"

        if use_http_pool:
            # Use HTTP actor pool for persistent connections
            raw_bytes_map = await _fetch_with_actor_pool(window_specs_batch)
        else:
            # Use existing httpx client (fallback)
            raw_bytes_map = await _fetch_with_httpx_client(window_specs_batch)

            # --- 2. Decode, Scale/Offset, Group (No changes needed here) ---
            processed_bands = defaultdict(dict)
            skipped_specs_count = 0
            for i, spec in enumerate(window_specs_batch):
                raw_bytes = raw_bytes_map.get(i)
                if raw_bytes is None:
                    # logger.warning(f"Skipping spec {i} (href={spec.cog_href}, band={spec.catalog_band_name}) due to fetch failure or zero size.")
                    skipped_specs_count += 1
                    continue
                try:
                    decode_shape = (spec.tile_shape_decode_h, spec.tile_shape_decode_w)
                    expected_shape = (spec.window_height, spec.window_width)
                    decoded_np = process._decode_apply_predictor(
                        raw_bytes, decode_shape, expected_shape, spec.dtype_str, spec.predictor
                    )
                    if decoded_np is None:
                        # logger.warning(f"Decoding failed for spec {i} (href={spec.cog_href}, band={spec.catalog_band_name}). Skipping.")
                        skipped_specs_count += 1
                        continue
                    processed_np = decoded_np.astype(np.float32)
                    if not (np.isclose(spec.scale_factor, 1.0) and np.isclose(spec.offset_factor, 0.0)):
                        processed_np = (processed_np * spec.scale_factor) + spec.offset_factor
                    spatial_key = (spec.scene_id, spec.tile_r, spec.tile_c)
                    processed_bands[spatial_key][spec.catalog_band_name] = processed_np
                except Exception as e:
                    logger.error(f"Error decoding/processing spec {i} (href={spec.cog_href}, band={spec.catalog_band_name}): {e}", exc_info=True)
                    skipped_specs_count += 1

            if not processed_bands:
                logger.warning(f"Worker processed {len(window_specs_batch)} specs, but none resulted in valid decoded data (skipped {skipped_specs_count}).")
                return False

            # --- 3. Stack, Apply Kernels, Assemble (No changes needed here) ---
            output_rows_list = []
            final_schema_fields = list(RASTER_CHUNK_SCHEMA)
            kernel_output_columns = {}
            logger.info(f"Stacking bands and applying kernels for {len(processed_bands)} spatial chunks...")
            logger.info(f"Worker Kernel Registry Contents: {list(KERNEL_REGISTRY.keys())}")

            for spatial_key, bands_dict in processed_bands.items():
                scene_id, tile_r, tile_c = spatial_key
                try:
                    if not bands_dict: continue
                    ref_spec = next((s for s in window_specs_batch if s.scene_id == scene_id and s.tile_r == tile_r and s.tile_c == tile_c), None)
                    if ref_spec is None: logger.error(f"Could not find reference WindowSpec for spatial key {spatial_key}. Skipping chunk."); continue
                    sorted_band_names = sorted(bands_dict.keys())
                    num_bands = len(sorted_band_names)
                    if num_bands == 0: continue
                    try:
                        stacked_np_chunk = np.stack([bands_dict[b] for b in sorted_band_names], axis=0)
                    except ValueError as stack_err:
                         logger.error(f"Failed to stack bands for spatial key {spatial_key}: {stack_err}. Bands present: {list(bands_dict.keys())}. Shapes: {[arr.shape for arr in bands_dict.values()]}. Skipping chunk.")
                         continue

                    chunk_shape = list(stacked_np_chunk.shape)
                    chunk_h, chunk_w = chunk_shape[1], chunk_shape[2]
                    list_offsets, list_values = engine_utils._np_chunk_to_arrow_list_components(stacked_np_chunk)
                    raster_data_pylist = list_values.to_pylist() if list_values is not None else []
                    ref_win = Window(ref_spec.window_col_off, ref_spec.window_row_off, ref_spec.window_width, ref_spec.window_height)
                    ref_affine = Affine(*ref_spec.cog_transform_elements)
                    ref_grid_def = {"transform": ref_affine, "tile_width": ref_spec.tile_shape_decode_w, "tile_height": ref_spec.tile_shape_decode_h}
                    bounds = grid.calculate_window_bounds(ref_spec.tile_r, ref_spec.tile_c, ref_win, ref_grid_def)
                    
                    datetime_obj: Optional[datetime] = None
                    if ref_spec.datetime_utc_iso:
                        try:
                            # Ensure timezone info is handled correctly
                            datetime_obj = datetime.fromisoformat(ref_spec.datetime_utc_iso.replace("Z", "+00:00"))
                        except Exception as dt_parse_err:
                            logger.warning(f"Failed to parse datetime string '{ref_spec.datetime_utc_iso}' in worker: {dt_parse_err}")
                            datetime_obj = None

                    chunk_id = f"{scene_id}_T{tile_r:02d}{tile_c:02d}_{uuid.uuid4().hex[:4]}"
                    # Create a new RecordBatch with the collection name in metadata
                    collection = ref_spec.collection
                    logger.info(f"Creating batch with collection: {collection}")
                    
                    # First create the schema with metadata
                    schema_with_metadata = RASTER_CHUNK_SCHEMA.with_metadata({
                        b"collection": collection.encode("utf-8")
                    })
                    
                    # Create tile coordinates as a map
                    tile_coords = pa.MapArray.from_arrays(
                        pa.array([0, 2]),  # offsets
                        pa.array(["tile_r", "tile_c"]),  # keys as flat array
                        pa.array([float(tile_r), float(tile_c)])  # values as flat array
                    )
                    
                    # Then create the batch with the schema that includes metadata
                    current_chunk_batch = pa.RecordBatch.from_arrays(
                        arrays=[
                            pa.array([chunk_id]),
                            pa.array([raster_data_pylist]),
                            pa.array([chunk_shape]),
                            pa.array([bounds]),
                            pa.array([ref_spec.cog_crs]),
                            pa.array([datetime_obj]),
                            pa.array([sorted_band_names]),
                            pa.array([None]),  # label
                            tile_coords  # Use the map array for tile coordinates
                        ],
                        schema=schema_with_metadata
                    )

                    if plan_apply_steps_serializable:
                        for step in plan_apply_steps_serializable:
                            func_id = step.get("function_id"); params = step.get("parameters", {})
                            logger.info(f"Applying kernel '{func_id}' to chunk {chunk_id}")
                            logger.info(f"Available bands in data: {sorted_band_names}")
                            try:
                                kernel_func = get_kernel_function(func_id)
                                if kernel_func:
                                    logger.info(f"Attempting to call kernel: ID='{func_id}', Function object: {kernel_func}, Params: {params}, Type of Params: {type(params)}")
                                    result_chunk_batch = kernel_func(current_chunk_batch, **params)  # Use **params to pass all parameters
                                    if not isinstance(result_chunk_batch, pa.RecordBatch) or result_chunk_batch.num_rows != 1: logger.warning(f"Kernel '{func_id}' did not return a valid single-row RecordBatch. Skipping kernel."); continue
                                    for field in result_chunk_batch.schema:
                                        if field.name not in current_chunk_batch.schema.names:
                                            logger.info(f"Kernel '{func_id}' added column '{field.name}' with type {field.type}")
                                            if field.name not in kernel_output_columns: kernel_output_columns[field.name] = field.type
                                            if field.name not in [f.name for f in final_schema_fields]: final_schema_fields.append(field)
                                    current_chunk_batch = result_chunk_batch
                                else: logger.warning(f"Kernel function '{func_id}' not found in registry. Skipping.")
                            except Exception as kernel_err: logger.error(f"Error applying kernel '{func_id}' to chunk {chunk_id}: {kernel_err}", exc_info=True); pass
                        # Only append the final processed batch
                        final_row_dict = current_chunk_batch.to_pylist()[0]
                        output_rows_list.append(final_row_dict)
                    else:
                        # If no apply steps, just append the original batch
                        row_data_dict = {}
                        for key, val in current_chunk_batch.to_pylist()[0].items():
                            if isinstance(val, pa.ListArray): row_data_dict[key] = val.to_pylist()[0]
                            elif isinstance(val, pa.Scalar): row_data_dict[key] = val.as_py()
                            else: row_data_dict[key] = val
                        output_rows_list.append(row_data_dict)
                except Exception as chunk_err:
                    logger.error(f"Error processing spatial chunk {spatial_key}: {chunk_err}", exc_info=True)

            if not output_rows_list:
                logger.warning(f"Worker processing yielded zero output rows after stacking/kernels.")
                return False

            try:
                final_schema = pa.schema(final_schema_fields)
                logger.debug(f"Final assembled schema for batch: {final_schema}")
                final_table = pa.Table.from_pylist(output_rows_list, schema=final_schema)
                batches = final_table.to_batches(max_chunksize=None)
                if not batches: logger.error("Failed to convert final table to RecordBatch."); return None

                # --- MODIFIED: Pass the address to the upload function ---
                upload_success = _upload_batch_to_flight_server(execution_id, batches[0], flight_address)

                if upload_success:
                    logger.info(f"Worker async logic finished. Uploaded batch with {batches[0].num_rows} rows.")
                    return True  # Return success status
                else:
                    error_msg = f"Worker failed to upload batch to Flight server for execution {execution_id}"
                    logger.error(error_msg)
                    raise RuntimeError(error_msg)  # Raise exception to fail the Ray task
            except Exception as final_batch_err:
                logger.exception(f"Failed to assemble final output RecordBatch: {final_batch_err}")
                raise RuntimeError(f"Failed to assemble final output RecordBatch: {final_batch_err}")

    # Run the async logic to get the processed batch
    final_result = asyncio.run(_run_async_logic())

    if final_result is False:
        logger.warning(f"Worker for {execution_id} produced no data. Returning success (no-op).")
        return True  # Still a success, just no data to upload

    # If we get here, the upload was successful
    return final_result


async def _fetch_with_actor_pool(window_specs_batch: List[WindowSpec]) -> Dict[int, bytes]:
    """
    Fetch COG data using HTTP actor pool for persistent connections.

    Args:
        window_specs_batch: List of WindowSpec objects to fetch

    Returns:
        Dictionary mapping spec index to raw bytes data
    """
    try:
        # Get HTTP actor pool
        http_actor_pool = ray.get_actor("http_actor_pool_manager")

        # Use distributed fetch for large batches
        if len(window_specs_batch) > 20:
            results = await http_actor_pool.fetch_batch_distributed.remote(window_specs_batch)
        else:
            # Use single actor for small batches
            actor = await http_actor_pool.get_next_actor.remote()
            results = await actor.fetch_batch.remote(window_specs_batch)

        # Convert results to index-based mapping
        raw_bytes_map = {}
        for i, spec in enumerate(window_specs_batch):
            if spec.byte_size <= 0:
                logger.debug(f"Skipping spec {i} due to zero byte size")
                continue

            # Create key that matches what HTTP actor returns
            key = f"{spec.cog_href}:{spec.byte_offset}-{spec.byte_offset + spec.byte_size - 1}"
            raw_bytes_map[i] = results.get(key)

        logger.info(f"HTTP actor pool fetched {len(raw_bytes_map)} results")
        return raw_bytes_map

    except Exception as e:
        logger.error(f"HTTP actor pool fetch failed: {e}")
        logger.info("Falling back to direct HTTP client")
        return await _fetch_with_httpx_client(window_specs_batch)


async def _fetch_with_httpx_client(window_specs_batch: List[WindowSpec]) -> Dict[int, bytes]:
    """
    Fetch COG data using direct httpx client (existing implementation).

    Args:
        window_specs_batch: List of WindowSpec objects to fetch

    Returns:
        Dictionary mapping spec index to raw bytes data
    """
    async with httpx.AsyncClient(http2=True, follow_redirects=True, timeout=60.0) as client:
        # --- 1. Fetch all raw data concurrently (existing logic) ---
        requests_by_url = defaultdict(list)
        original_indices = {}

        for i, spec in enumerate(window_specs_batch):
            if spec.byte_size <= 0:
                logger.debug(f"Skipping spec {i} (href={spec.cog_href}, band={spec.catalog_band_name}) due to zero byte size.")
                continue
            url = spec.cog_href
            fetch_request = {'offset': spec.byte_offset, 'size': spec.byte_size}
            requests_by_url[url].append(fetch_request)
            if url not in original_indices: original_indices[url] = []
            original_indices[url].append(i)

        fetch_coroutines = []
        url_order = list(requests_by_url.keys())
        for url in url_order:
            logger.debug(f"Creating fetch coroutine for {url} with {len(requests_by_url[url])} requests.")
            fetch_coroutines.append(fetch.fetch_raw_window_data(client, url, requests_by_url[url]))

        logger.info(f"Fetching data for {len(url_order)} URLs concurrently...")
        fetch_results_nested = await asyncio.gather(*fetch_coroutines, return_exceptions=True)
        logger.info("Fetching complete.")

        # --- Flatten results (existing logic) ---
        raw_bytes_map = {}
        for i, url in enumerate(url_order):
            results_for_url = fetch_results_nested[i]
            indices_for_url = original_indices[url]
            if isinstance(results_for_url, Exception):
                logger.error(f"Fetch failed entirely for URL {url}: {results_for_url}")
                for original_idx in indices_for_url: raw_bytes_map[original_idx] = None
            elif isinstance(results_for_url, list):
                if len(results_for_url) != len(indices_for_url):
                    logger.error(f"Mismatch in fetch results count for {url}. Expected {len(indices_for_url)}, got {len(results_for_url)}. Marking as failed.")
                    for original_idx in indices_for_url: raw_bytes_map[original_idx] = None
                else:
                    for j, res_bytes in enumerate(results_for_url):
                        original_idx = indices_for_url[j]
                        raw_bytes_map[original_idx] = res_bytes
            else:
                logger.error(f"Unexpected fetch result type for {url}: {type(results_for_url)}. Marking as failed.")
                for original_idx in indices_for_url: raw_bytes_map[original_idx] = None

        return raw_bytes_map