"""
Processing utilities for COG data.
"""
import logging, sys
import pyarrow as pa
import numpy as np
from typing import List, Optional, Tuple, Dict, Any
from rasterio.transform import Affine # For transform math if needed by bounds calc
from rasterio.windows import Window # For window math if needed by bounds calc
from collections import defaultdict
import hashlib
import uuid

# Assuming imagecodecs is available in the environment
import imagecodecs

# Import helpers from utils and grid
from .utils import _np_chunk_to_arrow_list_components
from .grid import calculate_window_bounds
# Import the output schema - use tfw_raster_schemas instead of terrafloww.libs.geoarrow_raster.schema
from tfw_raster_schemas.raster import RASTER_CHUNK_SCHEMA

import numpy as np
import pyarrow as pa
from .registry import register, get as get_fn
from .bands import (
    get_band_id,
    get_common_name,
    get_band_info,
    get_bands_in_group,
    RED, GREEN, BLUE, NIR, SWIR_1, SWIR_2
)


# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", stream=sys.stdout
)
logger = logging.getLogger(__name__)

# Import band utilities from our centralized module
from .bands import get_band_id, get_common_name, get_band_info, get_bands_in_group

# Common band constants for easy access
RED = 'red'  # Will be mapped to B04 for Sentinel-2
NIR = 'nir'  # Will be mapped to B08 for Sentinel-2
GREEN = 'green'
BLUE = 'blue'
SWIR_1 = 'swir_1'
SWIR_2 = 'swir_2'

def get_band_aliases(collection: str, band_name: str) -> List[str]:
    """
    Get all possible aliases for a band name in a collection.
    
    Args:
        collection: The collection name
        band_name: The band name to get aliases for
        
    Returns:
        A list of possible band name aliases
    """
    # For Sentinel-2, use our centralized band utilities
    if collection == "sentinel-2-l2a":
        try:
            band_id = get_band_id(band_name)
            aliases = {band_id, band_id.lower()}
            common_name = get_common_name(band_id)
            if common_name and common_name != band_id:
                aliases.add(common_name)
            return list(aliases)
        except ValueError:
            pass  # Not a standard Sentinel-2 band, continue with default
    
    # Default behavior for other collections
    return [band_name]

@register("terrafloww.spectral.ndvi")
def _ndvi_kernel(batch: pa.RecordBatch, red_band: str = 'red', nir_band: str = 'nir') -> pa.RecordBatch:
    """
    Calculate NDVI from a RecordBatch.
    
    Args:
        batch: The input RecordBatch containing band data
        red_band: Name or alias of the red band (default: 'red')
        nir_band: Name or alias of the near-infrared band (default: 'nir')
        
    Returns:
        RecordBatch with NDVI results appended
    """
    try:
        # Extract data from the first (only) row
        bands_list = batch.column("bands")[0].as_py()
        shape_list = batch.column("shape")[0].as_py()
        raster_data_flat = batch.column("raster_data")[0].as_py()
        
        # Get collection name from batch metadata
        collection = batch.schema.metadata.get(b"collection", b"").decode("utf-8")
        if not collection:
            logger.warning("NDVI kernel: No collection specified in metadata. Using default band names.")
            collection = "sentinel-2-l2a"  # Default to Sentinel-2 if not specified

        logger.info(f"NDVI kernel: Processing collection: {collection}")
        logger.info(f"NDVI kernel: Available bands: {bands_list}")
        logger.info(f"NDVI kernel: Requested bands - red: {red_band}, nir: {nir_band}")

        # Create case-insensitive band index mapping with all possible aliases
        band_indices = {}
        for i, band in enumerate(bands_list):
            # Add the band name as is
            band_indices[band.lower()] = i
            
            # If this is a Sentinel-2 collection, add common name aliases
            if collection == "sentinel-2-l2a":
                try:
                    # Add the standard band ID (e.g., 'B04' for 'red')
                    band_id = get_band_id(band)
                    band_indices[band_id.lower()] = i
                    
                    # Add the common name if different
                    common_name = get_common_name(band_id)
                    if common_name and common_name.lower() != band_id.lower():
                        band_indices[common_name.lower()] = i
                except ValueError:
                    # Not a standard Sentinel-2 band, skip adding aliases
                    pass

        logger.info(f"NDVI kernel: Band index mapping: {band_indices}")
        
        # Find indices for red and nir bands (case-insensitive)
        red_idx = None
        nir_idx = None
        
        # Try multiple possible names for each band
        red_names = [red_band.lower(), get_band_id(red_band).lower()] if red_band else []
        nir_names = [nir_band.lower(), get_band_id(nir_band).lower()] if nir_band else []
        
        # Find the first matching index for each band
        for name in red_names:
            if name in band_indices:
                red_idx = band_indices[name]
                logger.info(f"NDVI kernel: Found red band at index {red_idx} (name: {name})")
                break
                
        for name in nir_names:
            if name in band_indices:
                nir_idx = band_indices[name]
                logger.info(f"NDVI kernel: Found nir band at index {nir_idx} (name: {name})")
                break

        if red_idx is None or nir_idx is None:
            logger.error(f"NDVI kernel: Could not find required bands. Available bands: {bands_list}")
            logger.error(f"NDVI kernel: Looking for red band (tried: {red_names}), found: {red_idx}")
            logger.error(f"NDVI kernel: Looking for nir band (tried: {nir_names}), found: {nir_idx}")
            logger.error(f"NDVI kernel: Band index mapping: {band_indices}")
            return batch

        # Validate shape and band count
        if not bands_list or not shape_list or len(shape_list) != 3 or not raster_data_flat:
            logger.error("NDVI kernel: Invalid input data structure (bands, shape, or raster_data).")
            return batch

        num_bands, height, width = shape_list
        if len(bands_list) != num_bands:
            logger.error(f"NDVI kernel: Mismatch between bands list length ({len(bands_list)}) and shape[0] ({num_bands}).")
            return batch

        # Reshape flattened data into [C, H, W] numpy array
        raster_np = np.array(raster_data_flat, dtype=np.float32).reshape((num_bands, height, width))

        # Extract red and nir bands
        red_data = raster_np[red_idx]
        nir_data = raster_np[nir_idx]

        # Calculate NDVI
        denominator = nir_data + red_data
        ndvi_np = np.where(denominator != 0, (nir_data - red_data) / denominator, 0)

        # Flatten NDVI result
        ndvi_flat_list = ndvi_np.flatten().tolist()

        # Create Arrow array for NDVI column
        ndvi_arrow_array_for_column = pa.array([ndvi_flat_list], type=pa.list_(pa.float32()))

        # Append the new column to the original batch
        output_batch = batch.append_column("ndvi", ndvi_arrow_array_for_column)

        return output_batch

    except Exception as e:
        logger.error(f"NDVI kernel error: {e}", exc_info=True)
        return batch

def _decode_apply_predictor(
    raw_bytes: bytes,
    decode_shape: Tuple[int, int], # Full internal tile shape (H, W) for predictor
    expected_shape: Tuple[int, int], # Actual expected output shape (H, W)
    dtype_str: str,
    predictor: int
) -> Optional[np.ndarray]:
    """
    Internal helper: Decodes tile data, applies predictor, slices to expected shape.
    Moved from fetch.py.
    """
    if not raw_bytes:
        logger.warning("Received empty bytes for decoding.")
        return None
    try:
        # TODO: Add more sophisticated decompression based on COG compression tag if needed
        # For now, assuming ZLIB/Deflate is common from imagecodecs default handling? Check COG spec.
        # If other compressions like LZW are common, imagecodecs might need explicit calls.
        decompressed_data = imagecodecs.zlib_decode(raw_bytes) # Or other imagecodecs decoder

        # Reshape to the FULL internal tile shape first
        full_tile = np.frombuffer(decompressed_data, dtype=np.dtype(dtype_str)).reshape(decode_shape)

        # Apply predictor to the full tile before slicing
        if predictor == 2: # Horizontal differencing
            logger.debug("Applying Horizontal predictor (predictor=2)")
            # Use cumsum which is equivalent to reversing horizontal diff; handle dtypes
            if np.issubdtype(full_tile.dtype, np.integer):
                full_tile = np.cumsum(full_tile, axis=1, dtype=full_tile.dtype)
            elif np.issubdtype(full_tile.dtype, np.floating):
                 # Need to handle potential precision issues with float cumsum?
                 # Using float64 accumulator for intermediate sum might be safer
                 full_tile = np.cumsum(full_tile, axis=1, dtype=np.float64).astype(full_tile.dtype)
            else:
                 logger.warning(f"Predictor=2 (Horizontal) not fully supported for dtype {full_tile.dtype}. Skipping predictor.")

        elif predictor == 3: # Floating point predictor (more complex)
            # Requires imagecodecs with TIFFFP_SUPPORT enabled usually.
            # If imagecodecs handles it internally via tiff_decode, this might not be needed.
            # If implementing manually, it involves differences from neighbors.
            # See https://github.com/cgohlke/imagecodecs/blob/master/imagecodecs/imagecodecs.pyx#L6015
            logger.warning("Predictor=3 (Floating Point) is complex and potentially not fully handled here. Check imagecodecs capabilities.")
            # Placeholder: No operation for now if not handled by decoder implicitly
            pass
        elif predictor != 1: # Predictor 1 is None/No prediction
             logger.warning(f"Unsupported predictor value: {predictor}. Skipping predictor application.")

        # Slice the potentially predictor-applied array to the expected shape
        h_exp, w_exp = expected_shape
        if h_exp > full_tile.shape[0] or w_exp > full_tile.shape[1]:
             logger.error(f"Expected shape {expected_shape} is larger than decoded tile shape {full_tile.shape}. Cannot slice.")
             return None

        valid_tile_data = full_tile[:h_exp, :w_exp]

        # Final shape check after slicing
        if valid_tile_data.shape != expected_shape:
            logger.error(f"Shape mismatch after slicing: expected {expected_shape}, got {valid_tile_data.shape}. Check upstream logic.")
            return None

        return valid_tile_data

    except imagecodecs.ImagecodecsError as decode_error:
         logger.error(f"Imagecodecs decode error (shape={decode_shape}, dtype={dtype_str}, pred={predictor}): {decode_error}")
         return None
    except ValueError as reshape_error:
         logger.error(f"Reshape error after decode (shape={decode_shape}, dtype={dtype_str}, pred={predictor}): {reshape_error}")
         return None
    except Exception as e:
        logger.exception(f"Failed to decode/predict/slice tile (decode_shape={decode_shape}, expected={expected_shape}, dtype={dtype_str}, predictor={predictor}): {e}")
        return None

def process_raw_window_to_intermediate(
    raw_byte_list: List[Optional[bytes]],
    definitions_table: pa.Table
) -> Optional[pa.RecordBatch]:
    """
    Processes raw bytes + definitions into INTERMEDIATE_CHUNK_SCHEMA batches.
    Each row represents a single band for a specific spatial chunk.
    """
    if len(raw_byte_list) != len(definitions_table):
        logger.error(f"Mismatch bytes ({len(raw_byte_list)}) vs definitions ({len(definitions_table)}).")
        return None

    output_rows_pydict = defaultdict(list)
    definitions_list = definitions_table.to_pylist()

    for i, raw_bytes in enumerate(raw_byte_list):
        if raw_bytes is None: continue
        window_def = definitions_list[i]

        try:
            # 1. Decode & Scale/Offset -> NumPy [H, W] float32
            decode_shape = (window_def['tile_shape_decode_h'], window_def['tile_shape_decode_w'])
            expected_shape = (window_def['expected_shape_h'], window_def['expected_shape_w']) # H, W
            decoded_np = _decode_apply_predictor(raw_bytes, decode_shape, expected_shape, window_def['dtype_str'], window_def['predictor'])
            if decoded_np is None: continue

            scale = window_def['scale']; offset = window_def['offset_proc']
            processed_np = (decoded_np.astype(np.float32) * scale) + offset if not (np.isclose(scale, 1.0) and np.isclose(offset, 0.0)) else decoded_np.astype(np.float32)

            # 2. Calculate Keys & Metadata
            band_name = window_def['cog_key']
            band_chunk_id = f"{window_def['scene_id']}_{band_name}_r{window_def['tile_r']}_c{window_def['tile_c']}_{uuid.uuid4().hex[:6]}"

            # Create spatial key hash
            spatial_key_tuple = (window_def['scene_id'], window_def['tile_r'], window_def['tile_c'], window_def['window_col_off'], window_def['window_row_off'], window_def['window_width'], window_def['window_height'])
            spatial_key_str = "_".join(map(str, spatial_key_tuple))
            spatial_key_hash = hashlib.sha256(spatial_key_str.encode()).hexdigest()[:24]

            # Calculate bounds
            ref_transform = Affine(window_def['ref_transform_0'], window_def['ref_transform_1'], window_def['ref_transform_2'], window_def['ref_transform_3'], window_def['ref_transform_4'], window_def['ref_transform_5'])
            rio_window = Window(col_off=window_def['window_col_off'], row_off=window_def['window_row_off'], width=window_def['window_width'], height=window_def['window_height'])
            window_bounds = calculate_window_bounds(tile_r=window_def['tile_r'], tile_c=window_def['tile_c'], window=rio_window, grid_definition={"transform": ref_transform, "tile_width": window_def['tile_shape_decode_w'], "tile_height": window_def['tile_shape_decode_h']})

            # 3. Convert single-band numpy [H, W] to Arrow List<float32> format
            #    Need a helper for this specific conversion (1, H, W) -> flat list
            processed_np_1ch = processed_np[np.newaxis, :, :] # Add channel dim: [1, H, W]
            _, list_values = _np_chunk_to_arrow_list_components(processed_np_1ch) # Use existing helper
            raster_data_value = list_values.to_pylist() # Get flat list of floats

            # 4. Append data for INTERMEDIATE_CHUNK_SCHEMA
            output_rows_pydict["spatial_key"].append(spatial_key_hash)
            output_rows_pydict["band"].append(band_name)
            output_rows_pydict["band_chunk_id"].append(band_chunk_id)
            output_rows_pydict["band_raster_data"].append(raster_data_value)
            output_rows_pydict["band_shape"].append(list(processed_np_1ch.shape)) # [1, H, W]
            output_rows_pydict["bounds"].append(window_bounds)
            output_rows_pydict["crs"].append(window_def['crs'])
            output_rows_pydict["datetime"].append(window_def['datetime'])
            output_rows_pydict["label"].append(None)
            output_rows_pydict["quality"].append({"tile_row": float(window_def['tile_r']), "tile_col": float(window_def['tile_c'])})
            output_rows_pydict["scene_id"].append(window_def['scene_id'])

        except Exception as proc_err:
            logger.error(f"Error processing intermediate window {i} (asset {window_def.get('cog_key','?')}) in batch: {proc_err}", exc_info=True)

    # --- 5. Assemble final Intermediate RecordBatch ---
    if not output_rows_pydict["band_chunk_id"]:
        logger.warning("No intermediate windows were successfully processed in this batch.")
        return None

    logger.debug(f"Assembling intermediate RecordBatch from {len(output_rows_pydict['band_chunk_id'])} band-windows.")
    try:
        # Create intermediate table from pydict
        # Note: INTERMEDIATE_CHUNK_SCHEMA is not imported, so we'll use a generic approach
        intermediate_table = pa.Table.from_pydict(output_rows_pydict)
        batches = intermediate_table.to_batches(max_chunksize=None)
        if batches:
             logger.debug(f"Successfully assembled intermediate RecordBatch with {len(batches[0])} rows.")
             return batches[0]
        else: return None
    except Exception as batch_err:
        logger.exception(f"Failed to assemble intermediate RecordBatch: {batch_err}")
        return None


def process_raw_window_batch(
    raw_byte_list: List[Optional[bytes]],
    definitions_table: pa.Table # Arrow table slice for this batch
) -> Optional[pa.RecordBatch]:
    """
    Processes a batch of fetched raw window bytes using their definitions.
    Decodes, applies scale/offset, calculates metadata, and assembles
    into a single Arrow RecordBatch conforming to RASTER_CHUNK_SCHEMA.
    Returns None if the entire batch fails processing.
    """
    if len(raw_byte_list) != len(definitions_table):
        logger.error(f"Mismatch between fetched bytes ({len(raw_byte_list)}) and definitions ({len(definitions_table)}). Cannot process batch.")
        return None

    processed_windows_data = [] # List of tuples: (metadata_dict, offsets_pa, values_pa)

    # Convert definitions table to list of dicts for easier row-wise access
    # Perf: For very large batches, iterating row-wise might be better, but pylist is simpler.
    definitions_list = definitions_table.to_pylist()

    for i, raw_bytes in enumerate(raw_byte_list):
        if raw_bytes is None:
            logger.warning(f"Skipping window {i} in batch due to fetch failure.")
            continue # Skip windows that failed to fetch

        window_def = definitions_list[i]

        try:
            # --- 1. Decode ---
            decode_shape = (window_def['tile_shape_decode_h'], window_def['tile_shape_decode_w'])
            expected_shape = (window_def['expected_shape_h'], window_def['expected_shape_w'])
            decoded_np = _decode_apply_predictor(
                raw_bytes,
                decode_shape,
                expected_shape,
                window_def['dtype_str'],
                window_def['predictor']
            )
            if decoded_np is None:
                 logger.warning(f"Skipping window {i} (asset {window_def['cog_key']} r={window_def['tile_r']}, c={window_def['tile_c']}) due to decode failure.")
                 continue

            # --- 2. Scale & Offset ---
            scale = window_def['scale']; offset = window_def['offset_proc']
            needs_scaling = not (np.isclose(scale, 1.0) and np.isclose(offset, 0.0))
            if needs_scaling:
                processed_np = (decoded_np.astype(np.float32) * scale) + offset
            else:
                processed_np = decoded_np.astype(np.float32) # Ensure float32

            # --- 3. Calculate Metadata ---
            # Reconstruct Affine transform from components
            ref_transform = Affine(
                 window_def['ref_transform_0'], window_def['ref_transform_1'], window_def['ref_transform_2'],
                 window_def['ref_transform_3'], window_def['ref_transform_4'], window_def['ref_transform_5']
            )
            # Create rasterio Window object from definition
            rio_window = Window(
                 col_off=window_def['window_col_off'], row_off=window_def['window_row_off'],
                 width=window_def['window_width'], height=window_def['window_height']
            )
            # Use the grid helper to calculate precise bounds
            window_bounds = calculate_window_bounds(
                 tile_r=window_def['tile_r'], tile_c=window_def['tile_c'],
                 window=rio_window,
                 grid_definition={ # Reconstruct needed grid def parts
                      "transform": ref_transform, # Pass as Affine object
                      "tile_width": window_def['tile_shape_decode_w'], # Use decode width/height
                      "tile_height": window_def['tile_shape_decode_h']
                 }
            )

            # Prepare metadata dict for the output batch row
            chunk_id = f"{window_def['scene_id']}_{window_def['cog_key']}_r{window_def['tile_r']}_c{window_def['tile_c']}_{uuid.uuid4().hex[:4]}"
            chunk_meta = {
                "chunk_id": chunk_id,
                "bounds": window_bounds,
                "crs": window_def['crs'],
                "datetime": window_def['datetime'], # Should be python datetime UTC
                "bands": [window_def['cog_key']], # List with single band
                "label": None, # Placeholder
                "quality": { # Store provenance/quality info
                     "tile_row": float(window_def['tile_r']),
                     "tile_col": float(window_def['tile_c']),
                     # TODO: Add valid pixel percentage? Requires nodata value.
                }
            }

            # --- 4. Convert processed NumPy to Arrow components ---
            processed_chunk_np_c = processed_np[np.newaxis, :, :] # Add channel dim
            list_offsets, list_values = _np_chunk_to_arrow_list_components(processed_chunk_np_c)

            # Store components needed to build the final batch
            processed_windows_data.append((chunk_meta, list_offsets, list_values))

        except Exception as proc_err:
            logger.error(f"Error processing window {i} (asset {window_def.get('cog_key','?')}) in batch: {proc_err}", exc_info=True)
            # Skip this window on error

    # --- 5. Assemble final RecordBatch ---
    if not processed_windows_data:
        logger.warning("No windows were successfully processed in this batch.")
        return None

    logger.debug(f"Assembling RecordBatch from {len(processed_windows_data)} processed windows.")
    try:
        # (Use the same logic as _create_record_batch_from_processed helper, now inline)
        all_meta = [item[0] for item in processed_windows_data]
        all_offsets = [item[1] for item in processed_windows_data]
        all_values = [item[2] for item in processed_windows_data]

        # Combine list components
        combined_values = pa.concat_arrays(all_values)
        new_offsets = [0]; current_offset = 0
        for offsets_arr in all_offsets: size = offsets_arr[1].as_py(); current_offset += size; new_offsets.append(current_offset)
        combined_offsets = pa.array(new_offsets, type=pa.int32())
        raster_data_arr = pa.ListArray.from_arrays(combined_offsets, combined_values)

        # Create other arrays from metadata list
        chip_ids = [m['chunk_id'] for m in all_meta]
        # Shape: use the shape from the *first* processed chunk's array components? No, calculate from list_values/list_offsets
        shapes_list = []
        value_cursor = 0
        for meta, offsets_arr, values_arr in processed_windows_data:
             num_elements = offsets_arr[1].as_py() # Total elements C*H*W
             # Assuming C=1 (from np.newaxis)
             # Need H, W. These should match window_def['window_height'], window_def['window_width']?
             h = values_arr.type.shape[1] if hasattr(values_arr.type, 'shape') and len(values_arr.type.shape) > 1 else -1 # Placeholder, need robust way
             w = values_arr.type.shape[2] if hasattr(values_arr.type, 'shape') and len(values_arr.type.shape) > 2 else -1 # Placeholder
             # Let's try getting shape from definition more reliably
             win_def_idx = chip_ids.index(meta['chunk_id']) # Find original def (inefficient)
             win_def = definitions_list[win_def_idx] # Get corresponding def dict
             shape_actual = [1, win_def['window_height'], win_def['window_width']]
             shapes_list.append(shape_actual)
             value_cursor += num_elements

        bounds_list = [m['bounds'] for m in all_meta]
        crs_list = [m['crs'] for m in all_meta]
        datetime_list = [m['datetime'] for m in all_meta]
        bands_list = [m['bands'] for m in all_meta]
        label_list = [m.get('label') for m in all_meta]
        quality_list = [m.get('quality', {}) for m in all_meta]

        # Convert lists to Arrow Arrays
        chunk_id_arr = pa.array(chip_ids, type=pa.string())
        # Shape array
        flat_shapes = pa.array([dim for shape in shapes_list for dim in shape], type=pa.int32())
        shape_arr = pa.FixedSizeListArray.from_arrays(flat_shapes, list_size=3)
        # Bounds array
        flat_bounds = pa.array([b for bound in bounds_list for b in bound], type=pa.float64())
        bounds_arr = pa.FixedSizeListArray.from_arrays(flat_bounds, list_size=4)
        crs_arr = pa.array(crs_list, type=pa.string())
        datetime_arr = pa.array(datetime_list, type=pa.timestamp("us", tz="UTC"))
        # Bands array
        bands_offsets_list = [0]; current_bands_offset = 0; bands_values_list = []
        for b_list in bands_list: current_bands_offset += len(b_list); bands_offsets_list.append(current_bands_offset); bands_values_list.extend(b_list)
        bands_arr = pa.ListArray.from_arrays(pa.array(bands_offsets_list, type=pa.int32()), pa.array(bands_values_list, type=pa.string())) # Explicit types
        # Label array
        label_arr = pa.array(label_list, type=pa.string())
        # Quality array
        quality_offsets_list = [0]; current_quality_offset = 0; quality_keys_list = []; quality_values_list = []
        for q_map in quality_list:
            keys = list(q_map.keys()); vals = list(q_map.values()); current_quality_offset += len(keys)
            quality_offsets_list.append(current_quality_offset); quality_keys_list.extend(keys); quality_values_list.extend(vals)
        quality_map_arr = pa.MapArray.from_arrays(pa.array(quality_offsets_list, type=pa.int32()), pa.array(quality_keys_list, type=pa.string()), pa.array(quality_values_list, type=pa.float64())) # Explicit types

        arrays = [ chunk_id_arr, raster_data_arr, shape_arr, bounds_arr, crs_arr, datetime_arr, bands_arr, label_arr, quality_map_arr ]
        names = [f.name for f in RASTER_CHUNK_SCHEMA]

        final_batch = pa.RecordBatch.from_arrays(arrays, schema=RASTER_CHUNK_SCHEMA)
        logger.debug(f"Successfully assembled RecordBatch with {len(final_batch)} rows.")
        return final_batch

    except Exception as batch_err:
        logger.exception(f"Failed to assemble final RecordBatch: {batch_err}")
        return None
