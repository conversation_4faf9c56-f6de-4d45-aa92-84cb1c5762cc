"""
Utility functions for COG data processing.
"""
import pyarrow as pa
import numpy as np
import logging, sys
from typing import Dict, Any, List, Tuple, Union
from shapely.geometry.base import BaseGeometry
from shapely.ops import transform as shapely_transform
from pyproj import CRS, Transformer
from rasterio.transform import Affine

# Import the target output schema to get the correct dtype
from tfw_raster_schemas.raster import RASTER_CHUNK_SCHEMA

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", stream=sys.stdout
)
logger = logging.getLogger(__name__)

def _get_required_cog_info_from_record(
    record: Dict[str, Any],
    required_keys: List[str], 
) -> Dict[str, Any]:
    """
    Extracts essential COG info from a catalog record (dict),
    validates types, and sets defaults for scale/offset.
    Uses cog_* naming convention.
    """
    required_keys_static = [
        "cog_key", "cog_href", "cog_crs", "cog_transform",
        "cog_width", "cog_height", "cog_tile_width", "cog_tile_height",
        "cog_dtype", "cog_predictor", "cog_tile_offsets", "cog_tile_byte_counts",
        "cog_scale", "cog_offset"
    ]

    extracted = {key: record.get(key) for key in required_keys_static}
    missing = [key for key, value in extracted.items() if value is None and key not in ['cog_scale', 'cog_offset']]
    cog_key = extracted.get("cog_key", "UNKNOWN")

    if missing:
        logger.error(f"Missing essential keys {missing} in record for COG asset '{cog_key}'. Record data: {record}")
        raise ValueError(f"Missing essential keys: {missing} for COG asset '{cog_key}'")

    transform_input = extracted.get("cog_transform")
    try:
        if not isinstance(transform_input, (list, tuple)) or len(transform_input) != 6:
            raise ValueError(f"Expected 6 elements, got {len(transform_input) if isinstance(transform_input, (list, tuple)) else 'non-list/tuple'}")
        extracted["cog_transform_affine"] = Affine(*transform_input)
        extracted["cog_transform"] = tuple(transform_input) 
    except Exception as e:
        logger.error(f"Invalid cog_transform format: {transform_input} for asset '{cog_key}'. Error: {e}")
        raise ValueError(f"Invalid cog_transform format for asset '{cog_key}'") from e

    if not isinstance(extracted.get("cog_tile_offsets"), (list, tuple)):
        raise ValueError(f"Invalid cog_tile_offsets type for asset '{cog_key}': {type(extracted.get('cog_tile_offsets'))}")
    if not isinstance(extracted.get("cog_tile_byte_counts"), (list, tuple)):
        raise ValueError(f"Invalid cog_tile_byte_counts type for asset '{cog_key}': {type(extracted.get('cog_tile_byte_counts'))}")

    scale = extracted.get('cog_scale')
    offset = extracted.get('cog_offset')
    if scale is not None and not isinstance(scale, (int, float)):
        logger.warning(f"Invalid type for cog_scale ({type(scale)}) for asset '{cog_key}'. Treating as 1.0.")
        extracted['cog_scale'] = 1.0
    elif scale is None:
        extracted['cog_scale'] = 1.0 
    else:
        extracted['cog_scale'] = float(scale)

    if offset is not None and not isinstance(offset, (int, float)):
        logger.warning(f"Invalid type for cog_offset ({type(offset)}) for asset '{cog_key}'. Treating as 0.0.")
        extracted['cog_offset'] = 0.0
    elif offset is None:
        extracted['cog_offset'] = 0.0 
    else:
        extracted['cog_offset'] = float(offset)

    for key in ["cog_href", "cog_crs", "cog_width", "cog_height", "cog_tile_width", "cog_tile_height", "cog_dtype", "cog_predictor"]:
         if extracted.get(key) is None: 
              raise ValueError(f"Missing essential key '{key}' for COG asset '{cog_key}'")

    return extracted


def _np_chunk_to_arrow_list_components( 
    chunk_array: np.ndarray 
) -> Tuple[pa.Array, pa.Array]:
    """
    Converts a processed (C, H, W) NumPy chunk array (float32) into the
    flattened values array and the offsets array needed for an Arrow ListArray
    matching RASTER_CHUNK_SCHEMA's raster_data field.
    """
    logger.debug(">>> Entering _np_chunk_to_arrow_list_components")
    if not isinstance(chunk_array, np.ndarray) or chunk_array.ndim != 3:
         raise TypeError(f"Input chunk_array must be a 3D NumPy array (C, H, W). Got shape: {getattr(chunk_array, 'shape', 'N/A')}")
    if chunk_array.size == 0: raise ValueError("Input chunk_array is empty.")

    logger.debug(f"_np_chunk_to_arrow: Input chunk_array shape={chunk_array.shape}, dtype={chunk_array.dtype}")

    try:
        list_field = RASTER_CHUNK_SCHEMA.field("raster_data")
        target_value_type = list_field.type.value_type
        target_np_dtype = target_value_type.to_pandas_dtype()
        if not pa.types.is_floating(target_value_type):
            logger.warning(f"RASTER_CHUNK_SCHEMA expects non-float ({target_value_type}), but processing produces float32. Casting target to float32.")
            target_value_type = pa.float32()
            target_np_dtype = np.float32
    except Exception as e:
        logger.error(f"Schema inspection error: {e}", exc_info=True)
        target_value_type = pa.float32(); target_np_dtype = np.float32

    logger.debug(f"_np_chunk_to_arrow: Target Arrow type={target_value_type}, Target NumPy dtype={target_np_dtype}")

    if chunk_array.dtype != target_np_dtype:
        logger.warning(f"_np_chunk_to_arrow: Input dtype {chunk_array.dtype} differs from target {target_np_dtype}. Casting.")
        try:
            chunk_array = chunk_array.astype(target_np_dtype)
        except Exception as e:
            logger.error(f"Failed to cast input chunk to {target_np_dtype}: {e}")
            raise ValueError(f"Cannot cast input chunk data to {target_np_dtype}") from e

    flat_values = chunk_array.flatten()
    logger.debug(f"_np_chunk_to_arrow: flat_values dtype={flat_values.dtype}, size={flat_values.size}")

    try:
        values_array = pa.array(flat_values, type=target_value_type)
        logger.debug(f"_np_chunk_to_arrow: Created values_array type={values_array.type}")
    except Exception as e:
        logger.error(f"_np_chunk_to_arrow: Error creating values array: {e}", exc_info=True)
        raise

    offsets = pa.array([0, chunk_array.size], type=pa.int32())
    return offsets, values_array


def _reproject_geometry(
    geom: BaseGeometry, source_crs_str: str, target_crs_str: str
) -> BaseGeometry:
    """Reprojects a Shapely geometry using pyproj."""
    if not isinstance(geom, BaseGeometry):
         raise TypeError("Input 'geom' must be a Shapely geometry object.")
    if not source_crs_str or not target_crs_str:
         raise ValueError("Source and target CRS strings must be provided.")

    if source_crs_str == target_crs_str:
        logger.debug("Source and target CRS are the same, no reprojection needed.")
        return geom
    try:
        logger.debug(f"Reprojecting geometry from CRS '{source_crs_str}' to '{target_crs_str}'")
        source_crs = CRS.from_string(source_crs_str)
        target_crs = CRS.from_string(target_crs_str)
        project = Transformer.from_crs(source_crs, target_crs, always_xy=True).transform
        reprojected_geom = shapely_transform(project, geom)
        logger.debug("Reprojection successful.")
        return reprojected_geom
    except Exception as e:
        logger.error(f"Failed to reproject geometry from {source_crs_str} to {target_crs_str}: {e}", exc_info=True)
        raise ValueError(f"CRS Reprojection failed from '{source_crs_str}' to '{target_crs_str}': {e}") from e


def create_arrow_list_array(
    values: np.ndarray, offsets: np.ndarray
) -> pa.ListArray:
    """
    Creates an Arrow ListArray from values and offsets arrays.
    
    Args:
        values: NumPy array of values
        offsets: NumPy array of offsets
        
    Returns:
        Arrow ListArray
    """
    values_array = pa.array(values)
    offsets_array = pa.array(offsets)
    return pa.ListArray.from_arrays(offsets_array, values_array)


def calculate_resolution_from_transform(transform: Affine) -> Tuple[float, float]:
    """
    Calculates the resolution (pixel size) from an affine transform.
    
    Args:
        transform: Affine transform
        
    Returns:
        Tuple of (x_resolution, y_resolution)
    """
    x_resolution = abs(transform.a)
    y_resolution = abs(transform.e)
    return (x_resolution, y_resolution)


def format_datetime_filter(start_date: str, end_date: str) -> str:
    """
    Formats a datetime filter string from start and end dates.
    
    Args:
        start_date: Start date string (YYYY-MM-DD)
        end_date: End date string (YYYY-MM-DD)
        
    Returns:
        Formatted datetime filter string (YYYY-MM-DD/YYYY-MM-DD)
    """
    return f"{start_date}/{end_date}"
