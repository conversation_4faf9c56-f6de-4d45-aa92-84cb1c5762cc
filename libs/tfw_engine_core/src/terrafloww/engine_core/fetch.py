"""
Asynchronous fetch utilities for COG data.
"""
import asyncio
import httpx
import numpy as np
import logging, sys
from typing import List, Dict, Optional, Any

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", stream=sys.stdout
)
logger = logging.getLogger(__name__)


async def _fetch_range(
    client: httpx.AsyncClient, url: str, start: int, size: int, retries: int = 3
) -> bytes:
    """
    Fetches a specific byte range with retries using the provided client.
    
    Args:
        client: An httpx AsyncClient instance
        url: The URL to fetch from
        start: Starting byte position (inclusive)
        size: Number of bytes to fetch
        retries: Number of retry attempts
        
    Returns:
        The fetched bytes or empty bytes on failure
    """
    if size <= 0:
        logger.warning(f"Requested fetch size is zero or negative for {url} range {start}-{start+size-1}. Returning empty bytes.")
        return b""
        
    headers = {"Range": f"bytes={start}-{start+size-1}"}
    attempt = 0
    
    while attempt < retries:
        try:
            response = await client.get(url, headers=headers, follow_redirects=True)
            
            if response.status_code in (200, 206):  # OK or Partial Content
                return response.content
                
            # Log the error but continue with retry
            logger.warning(
                f"Failed to fetch range {start}-{start+size-1} from {url}: "
                f"HTTP {response.status_code}"
            )
        except Exception as e:
            logger.warning(
                f"Exception fetching range {start}-{start+size-1} from {url}: {str(e)}"
            )
            
        # Exponential backoff with jitter
        await asyncio.sleep(2 ** attempt * (0.5 + 0.5 * np.random.random()))
        attempt += 1
        
    # All retries failed
    logger.error(f"All {retries} retries failed for {url} range {start}-{start+size-1}")
    return b""


async def fetch_raw_window_data(
    client: httpx.AsyncClient,
    url: str,
    window_requests: List[Dict[str, Any]],
    max_concurrent: int = 20,
) -> List[Optional[bytes]]:
    """
    Fetches multiple raw byte ranges for a SINGLE URL concurrently.
    
    Args:
        client: An httpx AsyncClient instance
        url: The URL to fetch from
        window_requests: List of dicts with 'offset' and 'size' keys
        max_concurrent: Maximum number of concurrent requests
        
    Returns:
        A list of bytes objects (or None for failed requests)
        in the same order as the input window_requests
    """
    if not window_requests:
        return []
        
    # Create semaphore to limit concurrency
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def fetch_with_semaphore(req):
        async with semaphore:
            byte_offset = req.get("offset")
            byte_size = req.get("size")
            
            if byte_offset is None or byte_size is None:
                logger.error(f"Invalid window request: {req}")
                return None
                
            try:
                if byte_size <= 0:
                    logger.warning(f"Invalid byte size {byte_size} for request: {req}")
                    return None
                    
                return await _fetch_range(client, url, byte_offset, byte_size)
            except Exception as e:
                logger.error(f"Unhandled exception in fetch_with_semaphore: {str(e)}")
                return None
    
    # Create tasks for all requests
    tasks = [fetch_with_semaphore(req) for req in window_requests]
    
    # Wait for all tasks to complete
    results = await asyncio.gather(*tasks)
    
    # Return results in same order as requests
    return results
