"""
Band definitions and utilities for different satellite constellations.

This package provides standardized access to band information for various
satellite constellations, starting with Sentinel-2.
"""

from .sentinel2 import (
    SENTINEL2_BANDS,
    BAND_ALIASES,
    BAND_GROUPS,
    get_band_id,
    get_common_name,
    get_band_info,
    get_bands_in_group,
    RED, GREEN, BLUE, NIR, SWIR_1, SWIR_2
)

__all__ = [
    'SENTINEL2_BANDS',
    'BAND_ALIASES',
    'BAND_GROUPS',
    'get_band_id',
    'get_common_name',
    'get_band_info',
    'get_bands_in_group',
    'RED', 'GREEN', 'BLUE', 'NIR', 'SWIR_1', 'SWIR_2'
]
