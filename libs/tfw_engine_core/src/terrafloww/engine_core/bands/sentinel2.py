"""
Sentinel-2 band definitions and utilities.

This module provides standardized access to Sentinel-2 band information,
including band names, wavelengths, and resolutions.
"""
from typing import Dict, Tuple, Optional, List, Set

# Band: (Common Name, Center Wavelength (nm), Resolution (m))
SENTINEL2_BANDS: Dict[str, Tuple[str, Optional[float], Optional[int]]] = {
    # MSI Bands
    "B01": ("coastal_aerosol", 443, 60),
    "B02": ("blue", 490, 10),
    "B03": ("green", 560, 10),
    "B04": ("red", 665, 10),
    "B05": ("red_edge_1", 705, 20),
    "B06": ("red_edge_2", 740, 20),
    "B07": ("red_edge_3", 783, 20),
    "B08": ("nir", 842, 10),
    "B8A": ("nir_narrow", 865, 20),
    "B09": ("water_vapor", 945, 60),
    "B11": ("swir_1", 1610, 20),
    "B12": ("swir_2", 2190, 20),
    
    # Scene Classification
    "SCL": ("scene_classification", None, 20),
    
    # Atmospheric Correction
    "AOT": ("aerosol_optical_thickness", None, 10),
    "WVP": ("water_vapour", None, 10)
}

# Create reverse mapping for common names to band IDs
BAND_ALIASES: Dict[str, str] = {}
for band_id, (name, *_) in SENTINEL2_BANDS.items():
    BAND_ALIASES[name] = band_id
    BAND_ALIASES[band_id.lower()] = band_id  # Allow case-insensitive lookups

# Common band groups for easy access
BAND_GROUPS = {
    "visible": ["B02", "B03", "B04"],
    "red_edge": ["B05", "B06", "B07"],
    "nir": ["B08", "B8A"],
    "swir": ["B11", "B12"],
    "atmospheric": ["B01", "B09", "AOT", "WVP"]
}

def get_band_id(name: str) -> str:
    """
    Get standardized band ID from any valid alias.
    
    Args:
        name: Band ID or common name (case-insensitive)
        
    Returns:
        Standardized band ID (e.g., 'B08')
        
    Raises:
        ValueError: If the band name is empty or not found
    """
    if not name or not name.strip():
        raise ValueError("Band name cannot be empty or whitespace")
        
    # Normalize alias lookup to lowercase, fallback to upper-case ID
    key = name.lower().strip()
    if key in BAND_ALIASES:
        band_id = BAND_ALIASES[key]
    else:
        band_id = name.upper()

    if band_id not in SENTINEL2_BANDS:
        raise ValueError(f"Unknown band: {name!r}")
        
    return band_id

def get_common_name(band_id: str) -> str:
    """
    Get common name for a band ID if available.
    
    Args:
        band_id: Standardized band ID (e.g., 'B08')
        
    Returns:
        Common name (e.g., 'nir')
    """
    if band_id not in SENTINEL2_BANDS:
        raise ValueError(f"Unknown band ID: {band_id}")
    return SENTINEL2_BANDS[band_id][0]

def get_band_info(band_id: str) -> Tuple[str, Optional[float], Optional[int]]:
    """
    Get complete band information.
    
    Args:
        band_id: Standardized band ID
        
    Returns:
        Tuple of (common_name, wavelength_nm, resolution_m)
    """
    if band_id not in SENTINEL2_BANDS:
        raise ValueError(f"Unknown band ID: {band_id}")
    return SENTINEL2_BANDS[band_id]

def get_bands_in_group(group_name: str) -> List[str]:
    """
    Get all bands in a predefined group.
    
    Args:
        group_name: Name of the band group (e.g., 'visible', 'nir')
        
    Returns:
        List of band IDs in the group
    """
    return BAND_GROUPS.get(group_name.lower(), [])

# Export common band names for convenience
RED = "B04"
GREEN = "B03"
BLUE = "B02"
NIR = "B08"
SWIR_1 = "B11"
SWIR_2 = "B12"

__all__ = [
    'SENTINEL2_BANDS',
    'BAND_ALIASES',
    'BAND_GROUPS',
    'get_band_id',
    'get_common_name',
    'get_band_info',
    'get_bands_in_group',
    'RED', 'GREEN', 'BLUE', 'NIR', 'SWIR_1', 'SWIR_2'
]
