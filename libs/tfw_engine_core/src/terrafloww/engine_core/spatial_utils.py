# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# libs/tfw_engine_core/src/terrafloww/engine_core/spatial_utils.py

"""
Spatial utility functions for optimizing data access patterns.

Provides Hilbert curve indexing for spatial locality optimization,
improving S3 cache efficiency and reducing random access patterns.
"""

import logging
from typing import Tuple, Optional

logger = logging.getLogger(__name__)


def hilbert_index(tile_r: int, tile_c: int, order: int = 16) -> int:
    """
    Compute Hilbert curve index for spatial tile coordinates.
    
    The Hilbert curve is a space-filling curve that maps 2D coordinates
    to a 1D index while preserving spatial locality. Adjacent indices
    correspond to spatially adjacent tiles, improving cache efficiency.
    
    Args:
        tile_r: Tile row coordinate
        tile_c: Tile column coordinate  
        order: Hilbert curve order (determines max coordinate size)
               order=16 supports coordinates up to 2^16 = 65536
    
    Returns:
        Hilbert curve index as integer
        
    Example:
        >>> hilbert_index(0, 0)  # Top-left tile
        0
        >>> hilbert_index(0, 1)  # Adjacent tile
        1
        >>> hilbert_index(1, 1)  # Diagonal tile
        2
    """
    try:
        # Ensure coordinates are within bounds
        max_coord = 2 ** order
        if tile_r < 0 or tile_c < 0 or tile_r >= max_coord or tile_c >= max_coord:
            logger.warning(f"Tile coordinates ({tile_r}, {tile_c}) exceed order {order} bounds")
            # Clamp to valid range
            tile_r = max(0, min(tile_r, max_coord - 1))
            tile_c = max(0, min(tile_c, max_coord - 1))
        
        return _hilbert_xy_to_index(tile_r, tile_c, order)
        
    except Exception as e:
        logger.error(f"Failed to compute Hilbert index for ({tile_r}, {tile_c}): {e}")
        # Fallback to simple row-major ordering
        return tile_r * 1000 + tile_c


def _hilbert_xy_to_index(x: int, y: int, order: int) -> int:
    """
    Convert 2D coordinates to Hilbert curve index.
    
    Implementation of the Hilbert curve mapping algorithm.
    Based on the recursive definition of the Hilbert curve.
    
    Args:
        x: X coordinate (tile_r)
        y: Y coordinate (tile_c)
        order: Curve order
        
    Returns:
        Hilbert curve index
    """
    index = 0
    n = 2 ** order
    
    s = n // 2
    while s > 0:
        rx = 1 if (x & s) > 0 else 0
        ry = 1 if (y & s) > 0 else 0
        
        index += s * s * ((3 * rx) ^ ry)
        
        # Rotate coordinates if needed
        if ry == 0:
            if rx == 1:
                x = n - 1 - x
                y = n - 1 - y
            # Swap x and y
            x, y = y, x
            
        s //= 2
    
    return index


def hilbert_index_with_scene(scene_id: str, tile_r: int, tile_c: int, order: int = 16) -> Tuple[int, int]:
    """
    Compute Hilbert index with scene-based primary sorting.
    
    Returns a tuple (scene_hash, hilbert_index) that can be used for
    sorting to maintain temporal diversity while improving spatial locality.
    
    Args:
        scene_id: Scene identifier for temporal grouping
        tile_r: Tile row coordinate
        tile_c: Tile column coordinate
        order: Hilbert curve order
        
    Returns:
        Tuple of (scene_hash, hilbert_index) for sorting
    """
    try:
        # Simple hash of scene_id for temporal grouping
        scene_hash = hash(scene_id) % 10000  # Keep hash reasonable size
        hilbert_idx = hilbert_index(tile_r, tile_c, order)
        
        return (scene_hash, hilbert_idx)
        
    except Exception as e:
        logger.error(f"Failed to compute scene-aware Hilbert index: {e}")
        # Fallback to simple ordering
        return (hash(scene_id) % 10000, tile_r * 1000 + tile_c)


def compute_optimal_hilbert_order(max_tile_r: int, max_tile_c: int) -> int:
    """
    Compute optimal Hilbert curve order for given tile grid dimensions.
    
    Args:
        max_tile_r: Maximum tile row coordinate
        max_tile_c: Maximum tile column coordinate
        
    Returns:
        Optimal Hilbert curve order
    """
    try:
        # Find the minimum order that can accommodate the grid
        max_coord = max(max_tile_r, max_tile_c)
        
        # Find smallest power of 2 that is >= max_coord
        order = 0
        while (2 ** order) < max_coord:
            order += 1
        
        # Add some buffer and cap at reasonable maximum
        order = min(order + 2, 20)  # Cap at 2^20 = ~1M coordinates
        
        logger.info(f"Computed Hilbert order {order} for grid {max_tile_r}x{max_tile_c}")
        return order
        
    except Exception as e:
        logger.error(f"Failed to compute optimal Hilbert order: {e}")
        return 16  # Safe default


def test_hilbert_spatial_locality():
    """
    Test function to verify Hilbert curve spatial locality properties.
    
    Verifies that adjacent Hilbert indices correspond to spatially
    adjacent tiles, which is the key property for cache optimization.
    """
    logger.info("Testing Hilbert curve spatial locality...")
    
    # Test small grid
    test_coords = [
        (0, 0), (0, 1), (1, 0), (1, 1),
        (0, 2), (1, 2), (2, 0), (2, 1), (2, 2)
    ]
    
    # Compute Hilbert indices
    hilbert_indices = []
    for r, c in test_coords:
        idx = hilbert_index(r, c, order=4)
        hilbert_indices.append((idx, r, c))
    
    # Sort by Hilbert index
    hilbert_indices.sort()
    
    logger.info("Hilbert curve ordering:")
    for idx, r, c in hilbert_indices:
        logger.info(f"  Index {idx:3d}: tile ({r}, {c})")
    
    # Verify spatial locality: adjacent indices should be spatially close
    locality_score = 0
    for i in range(len(hilbert_indices) - 1):
        _, r1, c1 = hilbert_indices[i]
        _, r2, c2 = hilbert_indices[i + 1]
        
        # Manhattan distance between adjacent tiles in Hilbert order
        distance = abs(r2 - r1) + abs(c2 - c1)
        if distance <= 2:  # Adjacent or diagonal tiles
            locality_score += 1
    
    locality_ratio = locality_score / (len(hilbert_indices) - 1)
    logger.info(f"Spatial locality score: {locality_score}/{len(hilbert_indices)-1} = {locality_ratio:.2f}")
    
    if locality_ratio > 0.6:  # At least 60% of adjacent indices are spatially close
        logger.info("✓ Hilbert curve spatial locality test passed")
        return True
    else:
        logger.warning("⚠ Hilbert curve spatial locality test failed")
        return False


if __name__ == "__main__":
    # Run test when module is executed directly
    logging.basicConfig(level=logging.INFO)
    test_hilbert_spatial_locality()
