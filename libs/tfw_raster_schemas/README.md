# tfw-raster-schemas

Schema definitions for Terrafloww Platform, including Arrow schemas and Pydantic models.

## Installation

```bash
pip install tfw-raster-schemas
```

## Usage

```python
from tfw_raster_schemas import RASTER_CHUNK_SCHEMA
from tfw_raster_schemas.models import RasterMetadata

# Use the Arrow schema
table = pa.Table.from_arrays([...], schema=RASTER_CHUNK_SCHEMA)

# Use Pydantic models
metadata = RasterMetadata(...)
```
