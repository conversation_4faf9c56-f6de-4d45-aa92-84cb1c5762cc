"""
Operation schemas for the Terrafloww platform.

This module provides canonical definitions for operations that can be applied to geospatial data.
It serves as a single source of truth for operation metadata across the platform,
including the SDK and engine_core components.
"""

from typing import Dict, Any, List, Optional, TypedDict, Union

# Type definitions for operation schemas
class ParameterDefinition(TypedDict):
    """Definition of a parameter for an operation."""
    description: str
    type: str
    default: Optional[Union[str, int, float, bool, List, Dict]]
    required: bool

class OperationDefinition(TypedDict):
    """Definition of an operation."""
    description: str
    formula: Optional[str]
    parameters: Dict[str, Union[str, ParameterDefinition]]
    output_columns: List[str]
    required_bands: List[str]
    category: str

# Registry of available operations and their descriptions
# This is the canonical source of truth for operation definitions
OPERATIONS_SCHEMA = {
    "ndvi": {
        "description": "Normalized Difference Vegetation Index",
        "formula": "(NIR - Red) / (NIR + Red)",
        "parameters": {
            "red_band": {
                "description": "Name of the red band",
                "type": "string",
                "default": "red",
                "required": True
            },
            "nir_band": {
                "description": "Name of the near-infrared band",
                "type": "string",
                "default": "nir",
                "required": True
            }
        },
        "output_columns": ["ndvi"],
        "required_bands": ["red", "nir"],
        "category": "spectral"
    },
    "evi": {
        "description": "Enhanced Vegetation Index",
        "formula": "g * (NIR - Red) / (NIR + c1 * Red - c2 * Blue + l)",
        "parameters": {
            "red_band": {
                "description": "Name of the red band",
                "type": "string",
                "default": "red",
                "required": True
            },
            "nir_band": {
                "description": "Name of the near-infrared band",
                "type": "string",
                "default": "nir",
                "required": True
            },
            "blue_band": {
                "description": "Name of the blue band",
                "type": "string",
                "default": "blue",
                "required": True
            },
            "g": {
                "description": "Gain factor",
                "type": "float",
                "default": 2.5,
                "required": False
            },
            "c1": {
                "description": "Coefficient for the red band",
                "type": "float",
                "default": 6.0,
                "required": False
            },
            "c2": {
                "description": "Coefficient for the blue band",
                "type": "float",
                "default": 7.5,
                "required": False
            },
            "l": {
                "description": "Canopy background adjustment",
                "type": "float",
                "default": 1.0,
                "required": False
            }
        },
        "output_columns": ["evi"],
        "required_bands": ["red", "nir", "blue"],
        "category": "spectral"
    },
    "ndwi": {
        "description": "Normalized Difference Water Index",
        "formula": "(Green - NIR) / (Green + NIR)",
        "parameters": {
            "green_band": {
                "description": "Name of the green band",
                "type": "string",
                "default": "green",
                "required": True
            },
            "nir_band": {
                "description": "Name of the near-infrared band",
                "type": "string",
                "default": "nir",
                "required": True
            }
        },
        "output_columns": ["ndwi"],
        "required_bands": ["green", "nir"],
        "category": "spectral"
    },
    "normalized_diff": {
        "description": "Generic normalized difference operation",
        "formula": "(A - B) / (A + B)",
        "parameters": {
            "band_a": {
                "description": "Name of the first band",
                "type": "string",
                "default": None,
                "required": True
            },
            "band_b": {
                "description": "Name of the second band",
                "type": "string",
                "default": None,
                "required": True
            },
            "output_name": {
                "description": "Name for the output band",
                "type": "string",
                "default": "norm_diff",
                "required": False
            },
            "mask_zeros": {
                "description": "Whether to mask out zeros in the denominator",
                "type": "boolean",
                "default": True,
                "required": False
            }
        },
        "output_columns": ["<output_name>"],
        "required_bands": ["<band_a>", "<band_b>"],
        "category": "spectral"
    }
}

def get_operation_schema(operation_id: str) -> Optional[OperationDefinition]:
    """
    Get the schema for a specific operation.
    
    Args:
        operation_id: The ID of the operation
        
    Returns:
        The operation schema, or None if not found
    """
    return OPERATIONS_SCHEMA.get(operation_id)

def list_available_operations() -> List[str]:
    """
    List all available operations.
    
    Returns:
        A list of operation IDs
    """
    return list(OPERATIONS_SCHEMA.keys())

def get_operations_by_category(category: str) -> List[str]:
    """
    Get operations by category.
    
    Args:
        category: The category to filter by
        
    Returns:
        A list of operation IDs in the specified category
    """
    return [op_id for op_id, op_def in OPERATIONS_SCHEMA.items() 
            if op_def.get("category") == category]

def get_required_bands_for_operation(operation_id: str) -> List[str]:
    """
    Get the bands required for a specific operation.
    
    Args:
        operation_id: The ID of the operation
        
    Returns:
        A list of band names required for the operation, or an empty list if not found
    """
    operation_schema = get_operation_schema(operation_id)
    if not operation_schema:
        return []
    return operation_schema.get("required_bands", [])
