"""Grid template schema definitions for Terrafloww Platform."""

import pyarrow as pa

# Schema for Grid Templates Table
GRID_TEMPLATES_SCHEMA = pa.schema([
    pa.field("grid_id", pa.string(), nullable=False, 
             metadata={"description": "Unique identifier for this grid structure (e.g., S2_UTM31N_10m)"}),
    pa.field("crs", pa.string(), nullable=False,
             metadata={"description": "Coordinate Reference System string (e.g., 'EPSG:32631')"}),
    pa.field("image_width", pa.int64(), nullable=False,
             metadata={"description": "Total width of the scene grid in pixels"}),
    pa.field("image_height", pa.int64(), nullable=False,
             metadata={"description": "Total height of the scene grid in pixels"}),
    pa.field("tile_width", pa.int64(), nullable=False,
             metadata={"description": "Width of internal COG tiles in pixels"}),
    pa.field("tile_height", pa.int64(), nullable=False,
             metadata={"description": "Height of internal COG tiles in pixels"}),
    pa.field("transform", pa.list_(pa.float64(), 6), nullable=False,
             metadata={"description": "Affine transform coefficients [a, b, c, d, e, f] mapping pixel[col, row] to CRS[x, y]"}),
    pa.field("resolution_nominal", pa.float64(), nullable=True,
             metadata={"description": "Nominal resolution in CRS units (informative)"}),
    pa.field("created_at", pa.timestamp("us", tz="UTC"), nullable=False,
             metadata={"description": "Timestamp when this grid definition was first registered"}),
], metadata={b"description": b"Schema for grid template definitions used by the platform."})
