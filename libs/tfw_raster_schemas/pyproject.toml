[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[project]
name = "tfw-raster-schemas"
version = "0.1.0"
description = "Core Arrow schemas for Terrafloww raster data."
readme = "README.md"
requires-python = ">=3.10"
license = { text = "Proprietary" }
authors = [
    { name = "Terrafloww Team" },
]
dependencies = [
    "pyarrow>=15.0",
]

[project.urls]
"Homepage" = "https://github.com/terrafloww/platform"
"Bug Tracker" = "https://github.com/terrafloww/platform/issues"

[tool.hatch.build.targets.wheel]
packages = ["src/tfw_raster_schemas"]
