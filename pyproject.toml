# Root pyproject.toml (Focus on Tooling Config)

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"


[project]
# Minimal project info - this doesn't define the main installable package
name = "terrafloww-platform-monorepo" # Informative name for the workspace
version = "0.1.0"
description = "Monorepo for the Terrafloww Platform (SDK, Services, Libs)"
readme = "README.md"
requires-python = ">=3.10" # Recommended minimum for modern type hints/async
license = { text = "Proprietary" }
authors = [ { name = "Terrafloww Team" } ]
dependencies = [
    "aiohttp>=3.12.13",
    "boto3>=1.38.40",
    "deltalake>=1.0.2",
    "pyarrow>=15.0.0",
    "pystac-client>=0.8.6",
    "pytest>=8.4.1",
    "python-dotenv>=1.1.0",
    "ray>=2.47.1",
    "requests>=2.32.4",
    "s3fs>=0.4.2",
]
# NO core dependencies here - they belong in the individual packages

# Development dependencies for the entire repository
[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "mypy>=1.0.0",
    "ruff>=0.1.0",
    "grpcio-tools>=1.40.0",
]

# --- Tooling Configuration ---

[tool.ruff]
line-length = 100
target-version = "py312" # Match requires-python
select = ["E", "F", "I", "W", "C90", "UP"] # Select more checks (optional)
ignore = []
fixable = ["ALL"]
exclude = [
    ".git", ".venv", ".ruff_cache", "__pycache__", "build", "dist",
    "**/*_pb2.py", "**/*_pb2_grpc.py", "**/*_pb2.pyi",
]

[tool.ruff.isort]
known-first-party = ["terrafloww", "tfw_core_utils", "tfw_raster_schemas", "terrafloww.engine_core", "tfw_ray_utils"] # Add service namespaces if needed

[tool.pytest.ini_options]
testpaths = ["sdk/tests", "libs/*/tests", "services/*/tests"] # Include lib tests
python_files = "test_*.py"
python_functions = "test_*"
filterwarnings = ["ignore::DeprecationWarning"]
# Add asyncio mode if needed (uv should handle it based on pytest-asyncio install)
# asyncio_mode = "auto"

[tool.mypy]
python_version = ">3.10" # Match requires-python
mypy_path = "libs/*/src:sdk/src:services/*/src:services/*/app" # Adjust paths if using src layout in services
warn_return_any = true
warn_unused_configs = true
# Stricter checks (optional but recommended)
# disallow_untyped_defs = true
# disallow_incomplete_defs = true
# check_untyped_defs = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false

[[tool.mypy.overrides]]
module = "sdk.tests.*"
disallow_untyped_defs = false

[[tool.mypy.overrides]]
module = "libs.*.tests.*"
disallow_untyped_defs = false

[[tool.mypy.overrides]]
module = "services.*.tests.*"
disallow_untyped_defs = false

[[tool.mypy.overrides]]
module = "*_pb2.*"
disallow_untyped_defs = false

# If you have specific modules needing different rules:
# [[tool.mypy.overrides]]
# module = "terrafloww.some_module"