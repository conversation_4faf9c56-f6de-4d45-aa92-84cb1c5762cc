# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# test_http_actor_pool.py

"""
Test script for HTTP Actor Pool functionality.

Tests the new aiohttp-based HTTP connection pooling with Ray actors
to verify persistent connections and performance improvements.
"""

import asyncio
import os
import logging
import time
from typing import List

import ray

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Mock WindowSpec for testing
class MockWindowSpec:
    def __init__(self, cog_href: str, byte_offset: int, byte_size: int):
        self.cog_href = cog_href
        self.byte_offset = byte_offset
        self.byte_size = byte_size
        self.catalog_band_name = "test_band"


async def test_http_actor_pool():
    """Test HTTP actor pool initialization and basic functionality."""
    
    # Initialize Ray
    if not ray.is_initialized():
        ray.init(address="auto", ignore_reinit_error=True)
        logger.info("Ray initialized for testing")
    
    try:
        # Import after <PERSON> is initialized
        from terrafloww.engine_core.runtime_ray.actor_pool import get_http_actor_pool
        
        # Test 1: Initialize HTTP actor pool
        logger.info("=== Test 1: HTTP Actor Pool Initialization ===")
        start_time = time.time()
        
        http_actor_pool = await get_http_actor_pool()
        
        init_time = time.time() - start_time
        logger.info(f"HTTP actor pool initialized in {init_time:.2f}s")
        
        # Test 2: Get pool statistics
        logger.info("=== Test 2: Pool Statistics ===")
        stats = await http_actor_pool.get_pool_stats.remote()
        logger.info(f"Pool stats: {stats}")
        
        # Test 3: Health check
        logger.info("=== Test 3: Health Check ===")
        health = await http_actor_pool.health_check.remote()
        logger.info(f"Health status: {health}")
        
        # Test 4: Mock fetch test (if we have test URLs)
        logger.info("=== Test 4: Mock Fetch Test ===")
        
        # Create mock window specs for testing
        test_specs = [
            MockWindowSpec("https://httpbin.org/bytes/1024", 0, 512),
            MockWindowSpec("https://httpbin.org/bytes/1024", 512, 512),
        ]
        
        try:
            actor = await http_actor_pool.get_next_actor.remote()
            logger.info("Got HTTP actor for testing")
            
            # Test basic actor functionality (without actual fetch)
            actor_stats = await actor.get_stats.remote()
            logger.info(f"Actor stats: {actor_stats}")
            
        except Exception as e:
            logger.warning(f"Mock fetch test failed (expected): {e}")
        
        logger.info("=== HTTP Actor Pool Test Complete ===")
        return True
        
    except Exception as e:
        logger.error(f"HTTP actor pool test failed: {e}")
        return False


async def test_feature_flag():
    """Test feature flag functionality."""
    
    logger.info("=== Testing Feature Flag ===")
    
    # Test with feature flag disabled
    os.environ["TFW_USE_HTTP_POOL"] = "false"
    use_pool = os.environ.get("TFW_USE_HTTP_POOL", "false").lower() == "true"
    logger.info(f"TFW_USE_HTTP_POOL=false -> use_pool={use_pool}")
    
    # Test with feature flag enabled
    os.environ["TFW_USE_HTTP_POOL"] = "true"
    use_pool = os.environ.get("TFW_USE_HTTP_POOL", "false").lower() == "true"
    logger.info(f"TFW_USE_HTTP_POOL=true -> use_pool={use_pool}")
    
    # Reset to disabled for safety
    os.environ["TFW_USE_HTTP_POOL"] = "false"
    logger.info("Feature flag reset to disabled")


async def test_driver_integration():
    """Test RayDriver integration with HTTP actor pool."""
    
    logger.info("=== Testing RayDriver Integration ===")
    
    try:
        from terrafloww.engine_core.runtime_ray.driver import RayDriver
        
        # Test with feature flag disabled (should not initialize pool)
        os.environ["TFW_USE_HTTP_POOL"] = "false"
        driver = RayDriver()
        await driver._ensure_ray_initialized("auto")
        
        logger.info(f"Driver with disabled flag - http_actor_pool: {driver.http_actor_pool}")
        
        # Test with feature flag enabled (should initialize pool)
        os.environ["TFW_USE_HTTP_POOL"] = "true"
        driver2 = RayDriver()
        await driver2._ensure_ray_initialized("auto")
        
        logger.info(f"Driver with enabled flag - http_actor_pool: {driver2.http_actor_pool}")
        
        # Reset flag
        os.environ["TFW_USE_HTTP_POOL"] = "false"
        
        return True
        
    except Exception as e:
        logger.error(f"Driver integration test failed: {e}")
        return False


async def main():
    """Run all tests."""
    
    logger.info("Starting HTTP Actor Pool Tests")
    logger.info("=" * 50)
    
    # Test 1: Feature flag
    await test_feature_flag()
    
    # Test 2: HTTP actor pool
    pool_success = await test_http_actor_pool()
    
    # Test 3: Driver integration
    driver_success = await test_driver_integration()
    
    # Summary
    logger.info("=" * 50)
    logger.info("Test Summary:")
    logger.info(f"  HTTP Actor Pool: {'PASS' if pool_success else 'FAIL'}")
    logger.info(f"  Driver Integration: {'PASS' if driver_success else 'FAIL'}")
    
    if pool_success and driver_success:
        logger.info("🎉 All tests passed! HTTP optimization is ready for deployment.")
        logger.info("💡 Deploy with TFW_USE_HTTP_POOL=false for safe rollout")
    else:
        logger.error("❌ Some tests failed. Check implementation before deployment.")


if __name__ == "__main__":
    asyncio.run(main())
