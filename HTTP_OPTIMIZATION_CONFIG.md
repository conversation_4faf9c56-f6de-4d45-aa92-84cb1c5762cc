# HTTP Connection Optimization Configuration

## Feature Flag: TFW_USE_HTTP_POOL

Controls whether to use the new aiohttp-based HTTP actor pool for persistent connection pooling.

### Configuration

```bash
# Enable HTTP actor pool (for testing/production)
export TFW_USE_HTTP_POOL=true

# Disable HTTP actor pool (default - safe fallback)
export TFW_USE_HTTP_POOL=false
```

### Default Behavior

- **Default**: `false` (disabled)
- **Fallback**: Uses existing httpx per-task HTTP clients
- **Zero Risk**: Deploying with feature disabled has no impact on current performance

### Performance Impact

When enabled (`TFW_USE_HTTP_POOL=true`):

- **Target Improvement**: 20x faster (123s → 6s for NDVI time series)
- **Connection Reuse**: Persistent aiohttp sessions across Ray tasks
- **Range Merging**: Combines nearby byte ranges (50-80% fewer requests)
- **HTTP/2 Multiplexing**: Multiple requests per connection

### Deployment Strategy

#### Phase 1: Safe Deployment
```bash
# Deploy code with feature disabled
export TFW_USE_HTTP_POOL=false
# Zero impact on current performance
```

#### Phase 2: Staging Testing
```bash
# Enable in staging environment
export TFW_USE_HTTP_POOL=true
# Test NDVI processing performance
# Target: 123s → 30-60s (2-4x improvement)
```

#### Phase 3: Production Rollout
```bash
# Enable in production
export TFW_USE_HTTP_POOL=true
# Monitor performance and error rates
# Target: 6-15 second processing time
```

#### Phase 4: Cleanup
```bash
# Remove feature flag after validation
# Clean up legacy httpx code
```

### Monitoring

When HTTP actor pool is enabled, monitor:

- **Processing Time**: NDVI time series should improve from 123s to 6-15s
- **Connection Reuse**: Check actor pool statistics
- **Error Rates**: Ensure < 5% error rate
- **Memory Usage**: Monitor Ray worker memory consumption

### Rollback

If issues occur:

```bash
# Immediate rollback
export TFW_USE_HTTP_POOL=false

# Or git rollback
git checkout main
```

### Technical Details

- **HTTP Client**: aiohttp.ClientSession with persistent connections
- **Actor Pool**: 4 Ray actors with round-robin distribution
- **Connection Limits**: 100 total connections, 50 per host
- **Keepalive**: 300 seconds (5 minutes)
- **Health Monitoring**: Automatic connection reset on high error rates

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `TFW_USE_HTTP_POOL` | `false` | Enable/disable HTTP actor pool |
| `RAY_ADDRESS` | `auto` | Ray cluster address |
| `TFW_OUTPUT_STORAGE_PATH` | `/tmp/terrafloww/results` | Output storage path |

### Troubleshooting

#### HTTP Actor Pool Not Working
1. Check `TFW_USE_HTTP_POOL=true` is set
2. Verify Ray cluster is running
3. Check Ray logs for actor initialization errors
4. Monitor actor pool health status

#### Performance Not Improved
1. Verify feature flag is enabled
2. Check connection reuse statistics
3. Monitor network latency to COG sources
4. Validate range merging is working

#### High Error Rates
1. Check network connectivity to COG URLs
2. Monitor actor health status
3. Verify aiohttp session configuration
4. Check Ray cluster resource allocation

### Support

For issues with HTTP optimization:
1. Check Ray logs: `/ray_logs/`
2. Monitor actor pool statistics
3. Verify environment variable configuration
4. Test with feature flag disabled for comparison
