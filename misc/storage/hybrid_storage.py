# services/processing_engine/app/storage/hybrid_storage.py

import os
import logging
from typing import Dict, List, Optional, Set, Union
from pathlib import Path

import pyarrow as pa
import pyarrow.parquet as pq
from deltalake import DeltaTable, write_deltalake

from tfw_raster_schemas.raster import RASTER_CHUNK_SCHEMA

logger = logging.getLogger(__name__)

class HybridStorageManager:
    """
    Manages hybrid storage for geospatial data, separating index metadata 
    from large raster/vector data for optimal access patterns.
    
    Inspired by Space's storage architecture but optimized for geospatial.
    """
    
    def __init__(self, storage_path: str, schema: pa.Schema):
        """
        Initialize the hybrid storage manager.
        
        Args:
            storage_path: Base path for storage
            schema: Arrow schema for the data
        """
        self.storage_path = storage_path
        self.schema = schema
        
        # Create directories
        self.index_path = os.path.join(storage_path, "index")
        self.data_path = os.path.join(storage_path, "data")
        self.metadata_path = os.path.join(storage_path, "metadata")
        
        os.makedirs(self.index_path, exist_ok=True)
        os.makedirs(self.data_path, exist_ok=True)
        os.makedirs(self.metadata_path, exist_ok=True)
        
        # Identify field types from schema
        self.index_fields = []
        self.raster_fields = []
        self.vector_fields = []
        
        for field in schema:
            # Classify fields based on type or metadata
            if field.name == "raster_data" or field.name.endswith("_raster"):
                self.raster_fields.append(field.name)
            elif field.name == "geometry" or field.name.endswith("_geometry"):
                self.vector_fields.append(field.name)
            else:
                # All other fields go into index
                self.index_fields.append(field.name)
    
    def write_batch(self, batch: pa.RecordBatch) -> str:
        """
        Write a batch of data to hybrid storage.
        
        Args:
            batch: Arrow RecordBatch to write
            
        Returns:
            Unique batch ID
        """
        import uuid
        batch_id = str(uuid.uuid4())
        
        # 1. Separate index and data fields
        index_data = {
            field: batch[field] for field in self.index_fields
            if field in batch.schema.names
        }
        
        # Add batch_id to index
        index_data["batch_id"] = pa.array([batch_id] * len(batch))
        
        # 2. Write index data to Parquet
        index_table = pa.Table.from_arrays(
            list(index_data.values()),
            names=list(index_data.keys())
        )
        
        index_file = os.path.join(self.index_path, f"{batch_id}.parquet")
        pq.write_table(index_table, index_file)
        
        # 3. Write raster/vector data to separate files
        data_files = {}
        
        for field in self.raster_fields:
            if field in batch.schema.names:
                data_file = os.path.join(self.data_path, f"{batch_id}_{field}.array_record")
                # In practice, use array_record writer here
                # For MVP, use parquet
                field_table = pa.Table.from_arrays([batch[field]], names=[field])
                pq.write_table(field_table, data_file)
                data_files[field] = data_file
        
        for field in self.vector_fields:
            if field in batch.schema.names:
                data_file = os.path.join(self.data_path, f"{batch_id}_{field}.parquet")
                field_table = pa.Table.from_arrays([batch[field]], names=[field])
                pq.write_table(field_table, data_file)
                data_files[field] = data_file
        
        # 4. Update metadata
        metadata_file = os.path.join(self.metadata_path, "batches.delta")
        metadata_entry = {
            "batch_id": [batch_id],
            "index_file": [index_file],
            "data_files": [str(data_files)],  # Serialize dict to string
            "num_rows": [len(batch)],
            "timestamp": [pa.scalar(pd.Timestamp.now())]
        }
        
        metadata_table = pa.Table.from_pydict(metadata_entry)
        
        # Write to Delta table (append mode)
        try:
            # Check if table exists
            delta_table = DeltaTable(metadata_file)
            # Append to existing table
            write_deltalake(metadata_file, metadata_table, mode="append")
        except Exception:
            # Create new table
            write_deltalake(metadata_file, metadata_table, mode="overwrite")
        
        return batch_id
    
    def read_batch(self, batch_id: str) -> pa.RecordBatch:
        """
        Read a batch of data from hybrid storage.
        
        Args:
            batch_id: Batch ID to read
            
        Returns:
            Complete RecordBatch
        """
        # 1. Find metadata entry
        metadata_file = os.path.join(self.metadata_path, "batches.delta")
        delta_table = DeltaTable(metadata_file)
        
        # Filter to get specific batch
        metadata = delta_table.to_pyarrow_table(
            filters=[("batch_id", "=", batch_id)]
        )
        
        if len(metadata) == 0:
            raise ValueError(f"Batch {batch_id} not found")
        
        # 2. Read index file
        index_file = metadata["index_file"][0].as_py()
        index_table = pq.read_table(index_file)
        
        # 3. Read data files
        import ast
        data_files = ast.literal_eval(metadata["data_files"][0].as_py())
        
        # Prepare to reconstruct full batch
        fields = {}
        
        # Add index fields
        for field in index_table.column_names:
            if field != "batch_id":  # Skip batch_id
                fields[field] = index_table[field]
        
        # Add data fields
        for field, file_path in data_files.items():
            if os.path.exists(file_path):
                if field in self.raster_fields:
                    # In practice, use array_record reader here
                    # For MVP, use parquet
                    field_table = pq.read_table(file_path)
                    fields[field] = field_table[field]
                elif field in self.vector_fields:
                    field_table = pq.read_table(file_path)
                    fields[field] = field_table[field]
        
        # 4. Reconstruct batch
        reconstructed_table = pa.Table.from_arrays(
            list(fields.values()),
            names=list(fields.keys())
        )
        
        return reconstructed_table.to_batches()[0]
    
    def query(self, filters=None) -> pa.Table:
        """
        Query data using filters on index fields.
        
        Args:
            filters: PyArrow filters
            
        Returns:
            Table with matching data
        """
        # 1. Scan metadata to find all batches
        metadata_file = os.path.join(self.metadata_path, "batches.delta")
        delta_table = DeltaTable(metadata_file)
        metadata = delta_table.to_pyarrow_table()
        
        # 2. For each batch, filter on index and load matching data
        results = []
        
        for i in range(len(metadata)):
            batch_id = metadata["batch_id"][i].as_py()
            index_file = metadata["index_file"][i].as_py()
            
            # Read index
            index_table = pq.read_table(index_file)
            
            # Apply filters to index
            if filters:
                filtered_indices = index_table.filter(filters)
                if len(filtered_indices) > 0:
                    # Found matches, load full data
                    full_batch = self.read_batch(batch_id)
                    
                    # Extract only matching rows
                    # This is simplified - in practice would use row indices
                    results.append(filtered_indices)
            else:
                # No filters, include all
                results.append(index_table)
        
        if not results:
            # Return empty table with correct schema
            return pa.Table.from_batches([], schema=self.schema)
        
        # Combine results
        return pa.concat_tables(results)