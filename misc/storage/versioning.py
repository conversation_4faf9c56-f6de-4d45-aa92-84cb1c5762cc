# services/processing_engine/app/storage/versioning.py

import os
import logging
import uuid
from typing import Dict, List, Optional, Union
from datetime import datetime

import pyarrow as pa
from deltalake import DeltaTable, write_deltalake

logger = logging.getLogger(__name__)

class VersionManager:
    """
    Manages dataset versions with snapshots, branches, and tags.
    Inspired by Space but simplified for Terrafloww V2.
    """
    
    def __init__(self, storage_path: str):
        """
        Initialize the version manager.
        
        Args:
            storage_path: Base path for version storage
        """
        self.storage_path = storage_path
        self.versions_path = os.path.join(storage_path, "versions")
        os.makedirs(self.versions_path, exist_ok=True)
        
        self.snapshots_file = os.path.join(self.versions_path, "snapshots.delta")
        self.refs_file = os.path.join(self.versions_path, "refs.delta")
        self.current_file = os.path.join(self.versions_path, "current.delta")
        
        # Initialize tables if they don't exist
        self._init_tables()
    
    def _init_tables(self):
        """Initialize version tables if they don't exist."""
        # Snapshots table
        snapshots_schema = pa.schema([
            pa.field("snapshot_id", pa.string()),
            pa.field("parent_id", pa.string()),
            pa.field("timestamp", pa.timestamp('us')),
            pa.field("metadata", pa.map_(pa.string(), pa.string())),
            pa.field("batch_ids", pa.list_(pa.string())),
            pa.field("geospatial_extent", pa.list_(pa.float64())),  # [minx, miny, maxx, maxy]
            pa.field("commit_message", pa.string())
        ])
        
        # Refs table (branches and tags)
        refs_schema = pa.schema([
            pa.field("name", pa.string()),
            pa.field("type", pa.string()),  # "branch" or "tag"
            pa.field("snapshot_id", pa.string()),
            pa.field("timestamp", pa.timestamp('us'))
        ])
        
        # Current state
        current_schema = pa.schema([
            pa.field("current_branch", pa.string()),
            pa.field("current_snapshot_id", pa.string())
        ])
        
        try:
            # Try to load tables to check if they exist
            DeltaTable(self.snapshots_file)
        except Exception:
            # Create snapshots table
            empty_snapshots = pa.Table.from_pydict({
                "snapshot_id": [],
                "parent_id": [],
                "timestamp": [],
                "metadata": [],
                "batch_ids": [],
                "geospatial_extent": [],
                "commit_message": []
            }, schema=snapshots_schema)
            write_deltalake(self.snapshots_file, empty_snapshots)
        
        try:
            DeltaTable(self.refs_file)
        except Exception:
            # Create refs table with main branch
            empty_refs = pa.Table.from_pydict({
                "name": ["main"],
                "type": ["branch"],
                "snapshot_id": ["initial"],
                "timestamp": [pa.scalar(datetime.now())]
            }, schema=refs_schema)
            write_deltalake(self.refs_file, empty_refs)
        
        try:
            DeltaTable(self.current_file)
        except Exception:
            # Create current state table
            current_state = pa.Table.from_pydict({
                "current_branch": ["main"],
                "current_snapshot_id": ["initial"]
            }, schema=current_schema)
            write_deltalake(self.current_file, current_state)
    
    def create_snapshot(
        self, 
        batch_ids: List[str], 
        metadata: Dict[str, str] = None,
        geospatial_extent: List[float] = None,
        commit_message: str = "",
        parent_id: Optional[str] = None
    ) -> str:
        """
        Create a new snapshot.
        
        Args:
            batch_ids: List of batch IDs in this snapshot
            metadata: Optional metadata
            geospatial_extent: Optional bounding box [minx, miny, maxx, maxy]
            commit_message: Optional commit message
            parent_id: Parent snapshot ID (if None, uses current snapshot)
            
        Returns:
            New snapshot ID
        """
        # Generate new snapshot ID
        snapshot_id = str(uuid.uuid4())
        
        # Get current state
        current_table = DeltaTable(self.current_file).to_pyarrow_table()
        current_branch = current_table["current_branch"][0].as_py()
        
        # If parent_id not specified, use current snapshot
        if parent_id is None:
            current_snapshot_id = current_table["current_snapshot_id"][0].as_py()
            parent_id = current_snapshot_id
        
        # Create snapshot entry
        snapshot_data = {
            "snapshot_id": [snapshot_id],
            "parent_id": [parent_id],
            "timestamp": [pa.scalar(datetime.now())],
            "metadata": [metadata or {}],
            "batch_ids": [batch_ids],
            "geospatial_extent": [geospatial_extent or []],
            "commit_message": [commit_message]
        }
        
        # Write to snapshots table
        snapshot_table = pa.Table.from_pydict(snapshot_data)
        write_deltalake(self.snapshots_file, snapshot_table, mode="append")
        
        # Update current branch to point to new snapshot
        refs_table = DeltaTable(self.refs_file).to_pyarrow_table()
        
        # Find current branch reference
        branch_idx = None
        for i in range(len(refs_table)):
            if refs_table["name"][i].as_py() == current_branch and refs_table["type"][i].as_py() == "branch":
                branch_idx = i
                break
        
        if branch_idx is not None:
            # Update branch reference
            new_refs = pa.Table.from_pydict({
                "name": [current_branch],
                "type": ["branch"],
                "snapshot_id": [snapshot_id],
                "timestamp": [pa.scalar(datetime.now())]
            })
            
            # Replace existing branch entry
            # In real implementation, would use Delta MERGE operation
            # For MVP, recreate the table by concatenating non-matching rows and new row
            other_refs = refs_table.slice(0, branch_idx).append_row(refs_table.slice(branch_idx + 1))
            updated_refs = pa.concat_tables([other_refs, new_refs])
            write_deltalake(self.refs_file, updated_refs, mode="overwrite")
        
        # Update current snapshot
        new_current = pa.Table.from_pydict({
            "current_branch": [current_branch],
            "current_snapshot_id": [snapshot_id]
        })
        write_deltalake(self.current_file, new_current, mode="overwrite")
        
        return snapshot_id
    
    def checkout_branch(self, branch_name: str, create: bool = False) -> str:
        """
        Checkout a branch.
        
        Args:
            branch_name: Branch name to checkout
            create: Whether to create the branch if it doesn't exist
            
        Returns:
            Snapshot ID of the branch head
        """
        # Get refs table
        refs_table = DeltaTable(self.refs_file).to_pyarrow_table()
        
        # Check if branch exists
        branch_exists = False
        snapshot_id = None
        
        for i in range(len(refs_table)):
            if refs_table["name"][i].as_py() == branch_name and refs_table["type"][i].as_py() == "branch":
                branch_exists = True
                snapshot_id = refs_table["snapshot_id"][i].as_py()
                break
        
        if not branch_exists:
            if create:
                # Create new branch from current snapshot
                current_table = DeltaTable(self.current_file).to_pyarrow_table()
                current_snapshot_id = current_table["current_snapshot_id"][0].as_py()
                
                # Add new branch reference
                new_branch = pa.Table.from_pydict({
                    "name": [branch_name],
                    "type": ["branch"],
                    "snapshot_id": [current_snapshot_id],
                    "timestamp": [pa.scalar(datetime.now())]
                })
                
                write_deltalake(self.refs_file, new_branch, mode="append")
                snapshot_id = current_snapshot_id
            else:
                raise ValueError(f"Branch '{branch_name}' does not exist")
        
        # Update current state
        new_current = pa.Table.from_pydict({
            "current_branch": [branch_name],
            "current_snapshot_id": [snapshot_id]
        })
        write_deltalake(self.current_file, new_current, mode="overwrite")
        
        return snapshot_id
    
    def tag_snapshot(self, tag_name: str, snapshot_id: Optional[str] = None) -> str:
        """
        Create a tag pointing to a snapshot.
        
        Args:
            tag_name: Tag name
            snapshot_id: Snapshot ID to tag (if None, uses current snapshot)
            
        Returns:
            Tagged snapshot ID
        """
        # Get current snapshot if not specified
        if snapshot_id is None:
            current_table = DeltaTable(self.current_file).to_pyarrow_table()
            snapshot_id = current_table["current_snapshot_id"][0].as_py()
        
        # Check if tag already exists
        refs_table = DeltaTable(self.refs_file).to_pyarrow_table()
        
        for i in range(len(refs_table)):
            if refs_table["name"][i].as_py() == tag_name and refs_table["type"][i].as_py() == "tag":
                raise ValueError(f"Tag '{tag_name}' already exists")
        
        # Create new tag
        new_tag = pa.Table.from_pydict({
            "name": [tag_name],
            "type": ["tag"],
            "snapshot_id": [snapshot_id],
            "timestamp": [pa.scalar(datetime.now())]
        })
        
        write_deltalake(self.refs_file, new_tag, mode="append")
        
        return snapshot_id
    
    def get_snapshot(self, snapshot_id: Optional[str] = None) -> Dict:
        """
        Get snapshot details.
        
        Args:
            snapshot_id: Snapshot ID (if None, uses current snapshot)
            
        Returns:
            Snapshot details
        """
        # Get current snapshot if not specified
        if snapshot_id is None:
            current_table = DeltaTable(self.current_file).to_pyarrow_table()
            snapshot_id = current_table["current_snapshot_id"][0].as_py()
        
        # Get snapshot from table
        snapshots_table = DeltaTable(self.snapshots_file).to_pyarrow_table()
        
        for i in range(len(snapshots_table)):
            if snapshots_table["snapshot_id"][i].as_py() == snapshot_id:
                return {
                    "snapshot_id": snapshot_id,
                    "parent_id": snapshots_table["parent_id"][i].as_py(),
                    "timestamp": snapshots_table["timestamp"][i].as_py(),
                    "metadata": snapshots_table["metadata"][i].as_py(),
                    "batch_ids": snapshots_table["batch_ids"][i].as_py(),
                    "geospatial_extent": snapshots_table["geospatial_extent"][i].as_py(),
                    "commit_message": snapshots_table["commit_message"][i].as_py()
                }
        
        raise ValueError(f"Snapshot '{snapshot_id}' not found")
    
    def list_snapshots(self) -> pa.Table:
        """
        List all snapshots.
        
        Returns:
            Table of snapshots
        """
        return DeltaTable(self.snapshots_file).to_pyarrow_table()
    
    def list_branches(self) -> List[Dict]:
        """
        List all branches.
        
        Returns:
            List of branch details
        """
        refs_table = DeltaTable(self.refs_file).to_pyarrow_table()
        
        branches = []
        for i in range(len(refs_table)):
            if refs_table["type"][i].as_py() == "branch":
                branches.append({
                    "name": refs_table["name"][i].as_py(),
                    "snapshot_id": refs_table["snapshot_id"][i].as_py(),
                    "timestamp": refs_table["timestamp"][i].as_py()
                })
        
        return branches
    
    def list_tags(self) -> List[Dict]:
        """
        List all tags.
        
        Returns:
            List of tag details
        """
        refs_table = DeltaTable(self.refs_file).to_pyarrow_table()
        
        tags = []
        for i in range(len(refs_table)):
            if refs_table["type"][i].as_py() == "tag":
                tags.append({
                    "name": refs_table["name"][i].as_py(),
                    "snapshot_id": refs_table["snapshot_id"][i].as_py(),
                    "timestamp": refs_table["timestamp"][i].as_py()
                })
        
        return tags